using FluentAssertions;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Enums;

namespace RetailInventory.Domain.Tests.Entities;

public class ProductTests
{
    [Fact]
    public void Product_ShouldInitializeWithDefaultValues()
    {
        // Arrange & Act
        var product = new Product();

        // Assert
        product.Id.Should().Be(0);
        product.Name.Should().BeEmpty();
        product.Category.Should().Be(default(ProductCategory));
        product.Specifications.Should().BeEmpty();
        product.Grade.Should().BeEmpty();
        product.Color.Should().BeEmpty();
        product.CurrentQuantity.Should().Be(0);
        product.ReorderThreshold.Should().Be(0);
        product.UnitOfMeasure.Should().Be(default(UnitOfMeasure));
        product.IsActive.Should().BeTrue();
        product.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        product.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        product.StockBatches.Should().NotBeNull().And.BeEmpty();
        product.SaleLines.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void UpdateQuantity_WithValidQuantity_ShouldUpdateQuantityAndTimestamp()
    {
        // Arrange
        var product = new Product();
        var initialUpdateTime = product.UpdatedAt;
        var newQuantity = 50m;

        // Act
        Thread.Sleep(10); // Ensure time difference
        product.UpdateQuantity(newQuantity);

        // Assert
        product.CurrentQuantity.Should().Be(newQuantity);
        product.UpdatedAt.Should().BeAfter(initialUpdateTime);
    }

    [Fact]
    public void UpdateQuantity_WithNegativeQuantity_ShouldThrowArgumentException()
    {
        // Arrange
        var product = new Product();

        // Act & Assert
        var action = () => product.UpdateQuantity(-10m);
        action.Should().Throw<ArgumentException>()
            .WithMessage("Quantity cannot be negative");
    }

    [Fact]
    public void AddStock_WithValidQuantity_ShouldIncreaseCurrentQuantity()
    {
        // Arrange
        var product = new Product { CurrentQuantity = 10m };
        var addQuantity = 5m;
        var expectedQuantity = 15m;

        // Act
        product.AddStock(addQuantity);

        // Assert
        product.CurrentQuantity.Should().Be(expectedQuantity);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-5)]
    public void AddStock_WithInvalidQuantity_ShouldThrowArgumentException(decimal quantity)
    {
        // Arrange
        var product = new Product();

        // Act & Assert
        var action = () => product.AddStock(quantity);
        action.Should().Throw<ArgumentException>()
            .WithMessage("Quantity must be positive");
    }

    [Fact]
    public void ReduceStock_WithValidQuantity_ShouldDecreaseCurrentQuantity()
    {
        // Arrange
        var product = new Product { CurrentQuantity = 10m };
        var reduceQuantity = 3m;
        var expectedQuantity = 7m;

        // Act
        product.ReduceStock(reduceQuantity);

        // Assert
        product.CurrentQuantity.Should().Be(expectedQuantity);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-5)]
    public void ReduceStock_WithInvalidQuantity_ShouldThrowArgumentException(decimal quantity)
    {
        // Arrange
        var product = new Product { CurrentQuantity = 10m };

        // Act & Assert
        var action = () => product.ReduceStock(quantity);
        action.Should().Throw<ArgumentException>()
            .WithMessage("Quantity must be positive");
    }

    [Fact]
    public void ReduceStock_WithInsufficientStock_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var product = new Product { CurrentQuantity = 5m };
        var reduceQuantity = 10m;

        // Act & Assert
        var action = () => product.ReduceStock(reduceQuantity);
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Insufficient stock");
    }

    [Theory]
    [InlineData(10, 15, true)]  // Current < Threshold
    [InlineData(15, 15, true)]  // Current = Threshold
    [InlineData(20, 15, false)] // Current > Threshold
    public void IsLowStock_ShouldReturnCorrectValue(decimal currentQuantity, decimal threshold, bool expected)
    {
        // Arrange
        var product = new Product 
        { 
            CurrentQuantity = currentQuantity, 
            ReorderThreshold = threshold 
        };

        // Act & Assert
        product.IsLowStock.Should().Be(expected);
    }

    [Fact]
    public void GetAverageCostPerUnit_WithNoStockBatches_ShouldReturnZero()
    {
        // Arrange
        var product = new Product();

        // Act
        var averageCost = product.GetAverageCostPerUnit();

        // Assert
        averageCost.Should().Be(0);
    }

    [Fact]
    public void GetAverageCostPerUnit_WithStockBatches_ShouldCalculateWeightedAverage()
    {
        // Arrange
        var product = new Product();
        product.StockBatches.Add(new StockBatch 
        { 
            CostPerUnit = 100m, 
            RemainingQuantity = 10m 
        });
        product.StockBatches.Add(new StockBatch 
        { 
            CostPerUnit = 200m, 
            RemainingQuantity = 20m 
        });

        // Expected: (100 * 10 + 200 * 20) / (10 + 20) = 5000 / 30 = 166.67
        var expectedAverage = 166.67m;

        // Act
        var averageCost = product.GetAverageCostPerUnit();

        // Assert
        averageCost.Should().BeApproximately(expectedAverage, 0.01m);
    }

    [Fact]
    public void GetAverageCostPerUnit_WithEmptyStockBatches_ShouldReturnZero()
    {
        // Arrange
        var product = new Product();
        product.StockBatches.Add(new StockBatch 
        { 
            CostPerUnit = 100m, 
            RemainingQuantity = 0m 
        });

        // Act
        var averageCost = product.GetAverageCostPerUnit();

        // Assert
        averageCost.Should().Be(0);
    }

    [Fact]
    public void Product_ShouldUpdateTimestampOnStockOperations()
    {
        // Arrange
        var product = new Product();
        var initialTime = product.UpdatedAt;

        // Act
        Thread.Sleep(10);
        product.AddStock(10m);
        var afterAddTime = product.UpdatedAt;

        Thread.Sleep(10);
        product.ReduceStock(5m);
        var afterReduceTime = product.UpdatedAt;

        // Assert
        afterAddTime.Should().BeAfter(initialTime);
        afterReduceTime.Should().BeAfter(afterAddTime);
    }
}
