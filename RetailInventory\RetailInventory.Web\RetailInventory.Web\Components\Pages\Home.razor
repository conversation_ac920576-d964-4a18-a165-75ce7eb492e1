﻿@page "/"
@inject HttpClient Http
@inject ISnackbar Snackbar

<PageTitle>Dashboard - Retail Inventory</PageTitle>

<MudText Typo="Typo.h3" GutterBottom="true">📊 Dashboard</MudText>
<MudText Typo="Typo.body1" Class="mb-4">Welcome to your Retail Inventory System</MudText>

<!-- Quick Stats Cards -->
<MudGrid Class="mb-4">
    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="2" Class="pa-4">
            <MudCardContent Class="pa-0">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Success" Size="Size.Large" Class="mr-3" />
                    <div>
                        <MudText Typo="Typo.h4">₹@_dashboardData.TodaysSales.ToString("N0")</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Default">Today's Sales</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="2" Class="pa-4">
            <MudCardContent Class="pa-0">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.AccountBalance" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
                    <div>
                        <MudText Typo="Typo.h4">₹@_dashboardData.TodaysProfit.ToString("N0")</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Default">Today's Profit</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="2" Class="pa-4">
            <MudCardContent Class="pa-0">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                    <div>
                        <MudText Typo="Typo.h4">@_dashboardData.LowStockCount</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Default">Low Stock Items</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="2" Class="pa-4">
            <MudCardContent Class="pa-0">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Inventory" Color="Color.Info" Size="Size.Large" Class="mr-3" />
                    <div>
                        <MudText Typo="Typo.h4">₹@FormatLargeNumber(_dashboardData.InventoryValue)</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Default">Inventory Value</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

<!-- AI Features Section -->
<MudGrid Class="mb-4">
    <MudItem xs="12">
        <MudText Typo="Typo.h4" Class="mb-3">🤖 AI-Powered Features</MudText>
    </MudItem>

    <MudItem xs="12" md="4">
        <MudCard Elevation="3" Class="pa-4 h-100">
            <MudCardContent Class="pa-0">
                <div class="d-flex align-center mb-3">
                    <MudIcon Icon="@Icons.Material.Filled.CameraAlt" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
                    <MudText Typo="Typo.h5">📸 Snap & Sell</MudText>
                </div>
                <MudText Typo="Typo.body2" Class="mb-3">
                    Take a photo of any receipt and our AI will automatically extract products, quantities, and prices to create sales entries instantly.
                </MudText>
                <MudButton Variant="Variant.Filled" Color="Color.Primary" FullWidth="true" Href="/sales/receipt">
                    Try Snap & Sell
                </MudButton>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" md="4">
        <MudCard Elevation="3" Class="pa-4 h-100">
            <MudCardContent Class="pa-0">
                <div class="d-flex align-center mb-3">
                    <MudIcon Icon="@Icons.Material.Filled.Mic" Color="Color.Secondary" Size="Size.Large" Class="mr-3" />
                    <MudText Typo="Typo.h5">🎤 Voice Ledger</MudText>
                </div>
                <MudText Typo="Typo.body2" Class="mb-3">
                    Simply speak your sales: "Sold 3 Nike shoes for 15000 rupees" and watch the magic happen. No typing required!
                </MudText>
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" FullWidth="true" Href="/sales/voice">
                    Try Voice Entry
                </MudButton>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" md="4">
        <MudCard Elevation="3" Class="pa-4 h-100">
            <MudCardContent Class="pa-0">
                <div class="d-flex align-center mb-3">
                    <MudIcon Icon="@Icons.Material.Filled.AutoAwesome" Color="Color.Tertiary" Size="Size.Large" Class="mr-3" />
                    <MudText Typo="Typo.h5">🔮 Reorder Radar</MudText>
                </div>
                <MudText Typo="Typo.body2" Class="mb-3">
                    AI analyzes your sales patterns and predicts when you'll run out of stock. Get smart reorder suggestions with cost estimates.
                </MudText>
                <MudButton Variant="Variant.Filled" Color="Color.Tertiary" FullWidth="true" Href="/stock/reorder">
                    View Predictions
                </MudButton>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

<!-- Recent Activity & Quick Actions -->
<MudGrid>
    <MudItem xs="12" md="8">
        <MudCard Elevation="2">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">📈 Recent Sales Activity</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudList>
                    <MudListItem Icon="@Icons.Material.Filled.TrendingUp" IconColor="Color.Success">
                        <div class="d-flex justify-space-between align-center">
                            <div>
                                <MudText Typo="Typo.body1">Nike Air Max - Size 10</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">2 pairs sold for ₹9,000</MudText>
                            </div>
                            <MudText Typo="Typo.caption" Color="Color.Default">2 min ago</MudText>
                        </div>
                    </MudListItem>
                    <MudDivider />
                    <MudListItem Icon="@Icons.Material.Filled.Mic" IconColor="Color.Secondary">
                        <div class="d-flex justify-space-between align-center">
                            <div>
                                <MudText Typo="Typo.body1">Voice Sale: Adidas Ultraboost</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">1 pair sold for ₹6,200</MudText>
                            </div>
                            <MudText Typo="Typo.caption" Color="Color.Default">15 min ago</MudText>
                        </div>
                    </MudListItem>
                    <MudDivider />
                    <MudListItem Icon="@Icons.Material.Filled.CameraAlt" IconColor="Color.Primary">
                        <div class="d-flex justify-space-between align-center">
                            <div>
                                <MudText Typo="Typo.body1">Receipt Scan: Formal Shoes</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">3 pairs sold for ₹9,600</MudText>
                            </div>
                            <MudText Typo="Typo.caption" Color="Color.Default">1 hour ago</MudText>
                        </div>
                    </MudListItem>
                </MudList>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" md="4">
        <MudCard Elevation="2">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">⚡ Quick Actions</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="2">
                    <MudButton Variant="Variant.Outlined" Color="Color.Primary" FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Add" Href="/sales/manual">
                        New Sale
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Secondary" FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Inventory" Href="/stock/receive">
                        Receive Stock
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Info" FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Add" Href="/products/add">
                        Add Product
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Warning" FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Warning" Href="/stock/low-stock">
                        Check Low Stock
                    </MudButton>
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

@code {
    private DashboardData _dashboardData = new();
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            _loading = true;

            // Load dashboard metrics from API
            var response = await Http.GetFromJsonAsync<DashboardData>("api/reports/dashboard");
            if (response != null)
            {
                _dashboardData = response;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading dashboard data: {ex.Message}", Severity.Error);
            // Use default values if API fails
            _dashboardData = new DashboardData
            {
                TodaysSales = 45230,
                TodaysProfit = 12450,
                LowStockCount = 3,
                InventoryValue = 2850000
            };
        }
        finally
        {
            _loading = false;
        }
    }

    private string FormatLargeNumber(decimal value)
    {
        if (value >= 10000000) // 1 Crore
            return $"{value / 10000000:N1}Cr";
        else if (value >= 100000) // 1 Lakh
            return $"{value / 100000:N1}L";
        else if (value >= 1000) // 1 Thousand
            return $"{value / 1000:N1}K";
        else
            return value.ToString("N0");
    }

    public class DashboardData
    {
        public decimal TodaysSales { get; set; }
        public decimal TodaysProfit { get; set; }
        public int LowStockCount { get; set; }
        public decimal InventoryValue { get; set; }
    }
}
