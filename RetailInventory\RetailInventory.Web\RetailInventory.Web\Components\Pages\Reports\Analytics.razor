@page "/reports/analytics"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>Analytics Dashboard - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <div class="d-flex justify-space-between align-center mb-4">
        <div>
            <MudText Typo="Typo.h4">📊 Analytics Dashboard</MudText>
            <MudText Typo="Typo.body1" Color="Color.Default">
                Comprehensive business intelligence and performance insights
            </MudText>
        </div>
        <MudButton Variant="Variant.Outlined" 
                   Color="Color.Primary" 
                   StartIcon="@Icons.Material.Filled.Refresh"
                   OnClick="RefreshData">
            Refresh Data
        </MudButton>
    </div>

    @if (_loading)
    {
        <div class="d-flex justify-center pa-8">
            <MudProgressCircular Indeterminate="true" Size="Size.Large" />
        </div>
    }
    else
    {
        <!-- Key Metrics Cards -->
        <MudGrid Class="mb-6">
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="3" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Success" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">₹@_metrics.MonthlySales.ToString("N0")</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Monthly Sales</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Success">+12.5% vs last month</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="3" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">₹@_metrics.MonthlyProfit.ToString("N0")</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Monthly Profit</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Primary">@_metrics.AverageProfitMargin.ToString("N1")% margin</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="3" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.Inventory" Color="Color.Info" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">₹@FormatLargeNumber(_metrics.InventoryValue)</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Inventory Value</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Info">@_metrics.TotalProducts products</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="3" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_metrics.LowStockCount</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Low Stock Items</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Warning">Needs attention</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- Charts Section -->
        <MudGrid>
            <!-- Sales Trend Chart -->
            <MudItem xs="12" md="8">
                <MudCard Elevation="3">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">📈 Sales & Profit Trends (Last 30 Days)</MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudSelect @bind-Value="_chartPeriod" 
                                       Variant="Variant.Outlined" 
                                       Dense="true"
                                       OnSelectionChanged="OnChartPeriodChanged">
                                <MudSelectItem Value="@("Day")">Daily</MudSelectItem>
                                <MudSelectItem Value="@("Week")">Weekly</MudSelectItem>
                                <MudSelectItem Value="@("Month")">Monthly</MudSelectItem>
                            </MudSelect>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent>
                        <div id="salesTrendChart" style="height: 400px;"></div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- Category Revenue Pie Chart -->
            <MudItem xs="12" md="4">
                <MudCard Elevation="3">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">🥧 Revenue by Category</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <div id="categoryChart" style="height: 400px;"></div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- Top Products Performance -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="3">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">🏆 Top Performing Products</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_topProducts?.Any() == true)
                        {
                            <MudTable Items="_topProducts" Dense="true" Hover="true">
                                <HeaderContent>
                                    <MudTh>Product</MudTh>
                                    <MudTh>Revenue</MudTh>
                                    <MudTh>Profit</MudTh>
                                    <MudTh>Units</MudTh>
                                </HeaderContent>
                                <RowTemplate>
                                    <MudTd>
                                        <div>
                                            <MudText Typo="Typo.body2"><strong>@context.ProductName</strong></MudText>
                                            <MudText Typo="Typo.caption" Color="Color.Default">@context.Category</MudText>
                                        </div>
                                    </MudTd>
                                    <MudTd>₹@context.TotalRevenue.ToString("N0")</MudTd>
                                    <MudTd>
                                        <MudText Color="Color.Success">₹@context.TotalProfit.ToString("N0")</MudText>
                                    </MudTd>
                                    <MudTd>@context.UnitsSold</MudTd>
                                </RowTemplate>
                            </MudTable>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- Sales Source Analytics -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="3">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">📱 Sales Source Analytics</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_salesSources?.Any() == true)
                        {
                            <MudStack Spacing="3">
                                @foreach (var source in _salesSources)
                                {
                                    <div>
                                        <div class="d-flex justify-space-between align-center mb-1">
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@GetSourceIcon(source.Source)" Size="Size.Small" Class="mr-2" />
                                                <MudText Typo="Typo.body2">@source.Source</MudText>
                                            </div>
                                            <MudText Typo="Typo.body2"><strong>@source.Percentage.ToString("N1")%</strong></MudText>
                                        </div>
                                        <MudProgressLinear Value="@((double)source.Percentage)" Color="@GetSourceColor(source.Source)" />
                                        <div class="d-flex justify-space-between mt-1">
                                            <MudText Typo="Typo.caption">@source.SalesCount sales</MudText>
                                            <MudText Typo="Typo.caption">₹@source.TotalRevenue.ToString("N0")</MudText>
                                        </div>
                                    </div>
                                }
                            </MudStack>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- Inventory Analytics -->
            <MudItem xs="12">
                <MudCard Elevation="3">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">📦 Inventory Analytics</MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudButton Variant="Variant.Outlined" 
                                       Color="Color.Secondary" 
                                       StartIcon="@Icons.Material.Filled.FileDownload"
                                       Size="Size.Small">
                                Export
                            </MudButton>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_inventoryAnalytics?.Any() == true)
                        {
                            <MudTable Items="_inventoryAnalytics" Dense="true" Hover="true" FixedHeader="true" Height="300px">
                                <HeaderContent>
                                    <MudTh>Product</MudTh>
                                    <MudTh>Current Stock</MudTh>
                                    <MudTh>Stock Value</MudTh>
                                    <MudTh>Turnover Rate</MudTh>
                                    <MudTh>Days of Stock</MudTh>
                                    <MudTh>Status</MudTh>
                                </HeaderContent>
                                <RowTemplate>
                                    <MudTd>
                                        <div>
                                            <MudText Typo="Typo.body2"><strong>@context.ProductName</strong></MudText>
                                            <MudText Typo="Typo.caption" Color="Color.Default">@context.Category</MudText>
                                        </div>
                                    </MudTd>
                                    <MudTd>@context.CurrentStock.ToString("N0")</MudTd>
                                    <MudTd>₹@context.StockValue.ToString("N0")</MudTd>
                                    <MudTd>@context.TurnoverRate.ToString("N1")x</MudTd>
                                    <MudTd>@context.DaysOfStock days</MudTd>
                                    <MudTd>
                                        <MudChip Size="Size.Small" Color="@GetStockStatusColor(context.StockStatus)">
                                            @context.StockStatus
                                        </MudChip>
                                    </MudTd>
                                </RowTemplate>
                            </MudTable>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    }
</MudContainer>

@code {
    private bool _loading = true;
    private string _chartPeriod = "Day";
    private DashboardMetricsDto _metrics = new();
    private List<ProductPerformanceDto> _topProducts = new();
    private List<SalesSourceAnalyticsDto> _salesSources = new();
    private List<InventoryAnalyticsDto> _inventoryAnalytics = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadAnalyticsData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeCharts();
        }
    }

    private async Task LoadAnalyticsData()
    {
        try
        {
            _loading = true;

            // Load all analytics data in parallel
            var metricsTask = Http.GetFromJsonAsync<DashboardMetricsDto>("api/reports/dashboard");
            var topProductsTask = Http.GetFromJsonAsync<List<ProductPerformanceDto>>("api/reports/top-products?count=5");
            var salesSourcesTask = Http.GetFromJsonAsync<List<SalesSourceAnalyticsDto>>("api/reports/sales-source-analytics");
            var inventoryTask = Http.GetFromJsonAsync<List<InventoryAnalyticsDto>>("api/reports/inventory-analytics");

            await Task.WhenAll(metricsTask, topProductsTask, salesSourcesTask, inventoryTask);

            _metrics = await metricsTask ?? new DashboardMetricsDto();
            _topProducts = await topProductsTask ?? new List<ProductPerformanceDto>();
            _salesSources = await salesSourcesTask ?? new List<SalesSourceAnalyticsDto>();
            _inventoryAnalytics = await inventoryTask ?? new List<InventoryAnalyticsDto>();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading analytics data: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task InitializeCharts()
    {
        try
        {
            // Load chart data
            var salesChartData = await Http.GetFromJsonAsync<ChartDataDto>("api/reports/charts/sales");
            var categoryChartData = await Http.GetFromJsonAsync<ChartDataDto>("api/reports/charts/category-revenue");

            // Initialize charts using Chart.js
            await JSRuntime.InvokeVoidAsync("initializeSalesTrendChart", salesChartData);
            await JSRuntime.InvokeVoidAsync("initializeCategoryChart", categoryChartData);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error initializing charts: {ex.Message}", Severity.Warning);
        }
    }

    private async Task OnChartPeriodChanged(string period)
    {
        _chartPeriod = period;
        await RefreshCharts();
    }

    private async Task RefreshData()
    {
        await LoadAnalyticsData();
        await RefreshCharts();
        Snackbar.Add("Analytics data refreshed successfully!", Severity.Success);
    }

    private async Task RefreshCharts()
    {
        try
        {
            var startDate = DateTime.Today.AddDays(-30);
            var endDate = DateTime.Today;
            
            var salesChartData = await Http.GetFromJsonAsync<ChartDataDto>($"api/reports/charts/sales?startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}&groupBy={_chartPeriod}");
            await JSRuntime.InvokeVoidAsync("updateSalesTrendChart", salesChartData);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error refreshing charts: {ex.Message}", Severity.Warning);
        }
    }

    private string FormatLargeNumber(decimal value)
    {
        if (value >= 10000000) // 1 Crore
            return $"{value / 10000000:N1}Cr";
        else if (value >= 100000) // 1 Lakh
            return $"{value / 100000:N1}L";
        else if (value >= 1000) // 1 Thousand
            return $"{value / 1000:N1}K";
        else
            return value.ToString("N0");
    }

    private string GetSourceIcon(string source)
    {
        return source.ToLowerInvariant() switch
        {
            "manual" => Icons.Material.Filled.TouchApp,
            "voice" => Icons.Material.Filled.Mic,
            "receipt" => Icons.Material.Filled.CameraAlt,
            _ => Icons.Material.Filled.Receipt
        };
    }

    private Color GetSourceColor(string source)
    {
        return source.ToLowerInvariant() switch
        {
            "manual" => Color.Primary,
            "voice" => Color.Secondary,
            "receipt" => Color.Tertiary,
            _ => Color.Default
        };
    }

    private Color GetStockStatusColor(string status)
    {
        return status.ToLowerInvariant() switch
        {
            "critical" => Color.Error,
            "low" => Color.Warning,
            "normal" => Color.Success,
            _ => Color.Default
        };
    }
}
