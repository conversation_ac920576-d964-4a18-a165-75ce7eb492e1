@page "/sales/manual"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Manual Sale - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <div class="d-flex align-center mb-4">
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" 
                       Color="Color.Default" 
                       OnClick="GoBack" />
        <MudText Typo="Typo.h4" Class="ml-2">Manual Sale Entry</MudText>
    </div>

    <MudGrid>
        <!-- Sale Items -->
        <MudItem xs="12" md="8">
            <MudCard Elevation="3">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🛒 Sale Items</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Primary" 
                                   StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="AddSaleItem"
                                   Size="Size.Small">
                            Add Item
                        </MudButton>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    @if (_saleItems.Any())
                    {
                        <MudTable Items="_saleItems" Hover="true" Dense="true">
                            <HeaderContent>
                                <MudTh>Product</MudTh>
                                <MudTh>Quantity</MudTh>
                                <MudTh>Unit Price</MudTh>
                                <MudTh>Total</MudTh>
                                <MudTh>Actions</MudTh>
                            </HeaderContent>
                            <RowTemplate>
                                <MudTd>
                                    @if (context.Product != null)
                                    {
                                        <div>
                                            <MudText Typo="Typo.body1"><strong>@context.Product.Name</strong></MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Default">
                                                @context.Product.Specifications | Available: @context.Product.CurrentQuantity.ToString("N0")
                                            </MudText>
                                        </div>
                                    }
                                    else
                                    {
                                        <MudAutocomplete T="ProductDto"
                                                         @bind-Value="context.Product"
                                                         SearchFunc="SearchProducts"
                                                         ToStringFunc="@(p => p?.Name ?? "")"
                                                         Label="Select Product"
                                                         Variant="Variant.Outlined"
                                                         Dense="true"
                                                         OnSelectionChanged="@((ProductDto p) => OnProductSelected(context, p))">
                                            <ItemTemplate Context="product">
                                                <div>
                                                    <MudText Typo="Typo.body2"><strong>@product.Name</strong></MudText>
                                                    <MudText Typo="Typo.caption">Available: @product.CurrentQuantity.ToString("N0") @GetUnitDisplay(product.UnitOfMeasure)</MudText>
                                                </div>
                                            </ItemTemplate>
                                        </MudAutocomplete>
                                    }
                                </MudTd>
                                <MudTd>
                                    <MudNumericField @bind-Value="context.Quantity"
                                                     Min="0.01m"
                                                     Step="1"
                                                     Variant="Variant.Outlined"
                                                     Dense="true"
                                                     OnValueChanged="@(() => CalculateLineTotal(context))"
                                                     Disabled="context.Product == null" />
                                </MudTd>
                                <MudTd>
                                    <MudNumericField @bind-Value="context.SalePricePerUnit"
                                                     Min="0.01m"
                                                     Step="0.01m"
                                                     Variant="Variant.Outlined"
                                                     Dense="true"
                                                     Adornment="Adornment.Start"
                                                     AdornmentText="₹"
                                                     OnValueChanged="@(() => CalculateLineTotal(context))"
                                                     Disabled="context.Product == null" />
                                </MudTd>
                                <MudTd>
                                    <MudText Typo="Typo.body1"><strong>₹@context.LineTotal.ToString("N2")</strong></MudText>
                                </MudTd>
                                <MudTd>
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                   Color="Color.Error" 
                                                   Size="Size.Small"
                                                   OnClick="() => RemoveSaleItem(context)" />
                                </MudTd>
                            </RowTemplate>
                        </MudTable>
                    }
                    else
                    {
                        <div class="d-flex flex-column align-center pa-8">
                            <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Size="Size.Large" Color="Color.Default" Class="mb-4" />
                            <MudText Typo="Typo.h6" Color="Color.Default">No items added yet</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Default" Class="mb-4">
                                Click "Add Item" to start building your sale.
                            </MudText>
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Primary" 
                                       StartIcon="@Icons.Material.Filled.Add"
                                       OnClick="AddSaleItem">
                                Add First Item
                            </MudButton>
                        </div>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- Sale Summary -->
        <MudItem xs="12" md="4">
            <MudCard Elevation="3">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📊 Sale Summary</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <div>
                            <MudText Typo="Typo.body2" Color="Color.Default">Items Count:</MudText>
                            <MudText Typo="Typo.h6">@_saleItems.Count items</MudText>
                        </div>
                        
                        <MudDivider />
                        
                        <div>
                            <MudText Typo="Typo.body2" Color="Color.Default">Subtotal:</MudText>
                            <MudText Typo="Typo.h6">₹@_totalAmount.ToString("N2")</MudText>
                        </div>
                        
                        <div>
                            <MudText Typo="Typo.body2" Color="Color.Default">Total Cost:</MudText>
                            <MudText Typo="Typo.body1">₹@_totalCost.ToString("N2")</MudText>
                        </div>
                        
                        <div>
                            <MudText Typo="Typo.body2" Color="Color.Default">Profit:</MudText>
                            <MudText Typo="Typo.h6" Color="@(_totalProfit >= 0 ? Color.Success : Color.Error)">
                                ₹@_totalProfit.ToString("N2")
                            </MudText>
                        </div>
                        
                        <div>
                            <MudText Typo="Typo.body2" Color="Color.Default">Profit Margin:</MudText>
                            <MudText Typo="Typo.body1" Color="@(_profitMargin >= 0 ? Color.Success : Color.Error)">
                                @_profitMargin.ToString("N1")%
                            </MudText>
                        </div>
                        
                        <MudDivider />
                        
                        <!-- Customer Info -->
                        <div>
                            <MudTextField @bind-Value="_customerName"
                                          Label="Customer Name (Optional)"
                                          Variant="Variant.Outlined"
                                          Dense="true" />
                        </div>
                        
                        <div>
                            <MudTextField @bind-Value="_saleNotes"
                                          Label="Sale Notes (Optional)"
                                          Variant="Variant.Outlined"
                                          Lines="2"
                                          Dense="true" />
                        </div>
                        
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Success"
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.PointOfSale"
                                   OnClick="ProcessSale"
                                   Disabled="@(!_saleItems.Any() || _isProcessing)"
                                   Size="Size.Large">
                            @if (_isProcessing)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                <span>Processing...</span>
                            }
                            else
                            {
                                <span>Complete Sale</span>
                            }
                        </MudButton>
                    </MudStack>
                </MudCardContent>
            </MudCard>

            <!-- Quick Actions -->
            <MudCard Elevation="2" Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">⚡ Quick Actions</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="2">
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Secondary" 
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.Mic"
                                   Href="/sales/voice">
                            🎤 Voice Entry
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Tertiary" 
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.CameraAlt"
                                   Href="/sales/receipt">
                            📸 Snap & Sell
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Info" 
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.History"
                                   Href="/sales/history">
                            Sales History
                        </MudButton>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private List<ProductDto> _products = new();
    private List<SaleItemDto> _saleItems = new();
    private string _customerName = "";
    private string _saleNotes = "";
    private bool _isProcessing = false;

    private decimal _totalAmount => _saleItems.Sum(item => item.LineTotal);
    private decimal _totalCost => _saleItems.Sum(item => item.Quantity * (item.Product?.GetAverageCostPerUnit() ?? 0));
    private decimal _totalProfit => _totalAmount - _totalCost;
    private decimal _profitMargin => _totalAmount > 0 ? (_totalProfit / _totalAmount) * 100 : 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
        AddSaleItem(); // Start with one empty item
    }

    private async Task LoadProducts()
    {
        try
        {
            var response = await Http.GetFromJsonAsync<List<ProductDto>>("api/products");
            _products = response?.Where(p => p.CurrentQuantity > 0).ToList() ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading products: {ex.Message}", Severity.Error);
        }
    }

    private async Task<IEnumerable<ProductDto>> SearchProducts(string value)
    {
        if (string.IsNullOrEmpty(value))
            return _products.Take(10);

        return _products.Where(p => 
            p.Name.Contains(value, StringComparison.OrdinalIgnoreCase) ||
            p.Specifications.Contains(value, StringComparison.OrdinalIgnoreCase))
            .Take(10);
    }

    private void AddSaleItem()
    {
        _saleItems.Add(new SaleItemDto());
    }

    private void RemoveSaleItem(SaleItemDto item)
    {
        _saleItems.Remove(item);
    }

    private void OnProductSelected(SaleItemDto item, ProductDto? product)
    {
        item.Product = product;
        if (product != null)
        {
            // Set default quantity to 1
            item.Quantity = 1;
            // Set suggested price (cost + 50% markup)
            item.SalePricePerUnit = product.GetAverageCostPerUnit() * 1.5m;
            CalculateLineTotal(item);
        }
    }

    private void CalculateLineTotal(SaleItemDto item)
    {
        item.LineTotal = item.Quantity * item.SalePricePerUnit;
        StateHasChanged();
    }

    private async Task ProcessSale()
    {
        if (!_saleItems.Any() || _saleItems.Any(item => item.Product == null))
        {
            Snackbar.Add("Please add at least one valid item to the sale", Severity.Warning);
            return;
        }

        // Validate stock availability
        foreach (var item in _saleItems)
        {
            if (item.Product!.CurrentQuantity < item.Quantity)
            {
                Snackbar.Add($"Insufficient stock for {item.Product.Name}. Available: {item.Product.CurrentQuantity:N0}", Severity.Error);
                return;
            }
        }

        try
        {
            _isProcessing = true;

            var saleDto = new CreateSaleDto
            {
                CustomerName = _customerName,
                Notes = _saleNotes,
                SaleLines = _saleItems.Select(item => new SaleLineDto
                {
                    ProductId = item.Product!.Id,
                    Quantity = item.Quantity,
                    SalePricePerUnit = item.SalePricePerUnit
                }).ToList()
            };

            var response = await Http.PostAsJsonAsync("api/sales/manual", saleDto);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                Snackbar.Add($"Sale completed successfully! Total: ₹{_totalAmount:N2}, Profit: ₹{_totalProfit:N2}", Severity.Success);
                Navigation.NavigateTo("/");
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"Error processing sale: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error processing sale: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isProcessing = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/");
    }

    private string GetUnitDisplay(UnitOfMeasure unit)
    {
        return unit switch
        {
            UnitOfMeasure.Pairs => "pairs",
            UnitOfMeasure.Pieces => "pcs",
            UnitOfMeasure.Kilograms => "kg",
            UnitOfMeasure.Meters => "m",
            UnitOfMeasure.Liters => "L",
            _ => "units"
        };
    }

    public class SaleItemDto
    {
        public ProductDto? Product { get; set; }
        public decimal Quantity { get; set; } = 1;
        public decimal SalePricePerUnit { get; set; }
        public decimal LineTotal { get; set; }
    }

    public class CreateSaleDto
    {
        public string CustomerName { get; set; } = "";
        public string Notes { get; set; } = "";
        public List<SaleLineDto> SaleLines { get; set; } = new();
    }

    public class SaleLineDto
    {
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal SalePricePerUnit { get; set; }
    }
}
