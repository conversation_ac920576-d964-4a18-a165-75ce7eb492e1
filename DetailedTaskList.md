# Product Inventory System - Detailed Task List

You are the solo architect & developer.  
Goal: deliver a runnable **local prototype** of an inventory + sales system for
retail businesses (leather shops, shoe stores, clothing boutiques, etc.).  
Users can add product stock in various units (kg, pieces, pairs), record
sales, see automatic cost / profit, and attach rich metadata for different
product types (leather, shoes, clothing, accessories).

**🏪 Business Types Supported:**
- **Leather Shop**: Manage leather by weight (kg) or area (sq meters), track thickness, grade, finish
- **Shoe Store**: Manage footwear by pairs, track sizes, brands, styles, materials  
- **Clothing Store**: Manage garments by pieces, track sizes, colors, materials, styles
- **General Retail**: Configurable for any product type with flexible metadata

────────────────────────────────────────────────────────────────────────────
## 1️⃣  DEV ENVIRONMENT ASSUMPTIONS
────────────────────────────────────────────────────────────────────────────
• OS……….. Windows 10/11, VS 2022 or VS Code + C# extension  
• SDK………. .NET 8 (LTS)  
• DB……….. Local SQL Server installed on machine
   • Integrated Authentication  
   • DB name:  RetailInventoryDb  (you will need to create this)
• ORMs……. EF Core 8, Code-First  
• Frontend… Blazor WebAssembly Hosted (ASP.NET Core Hosted template)  
• Auth…….. NONE for now (simple local prototype).  Roles will be added later.  
• AI ………  Stubbed: a local service returns a fake parsed-receipt JSON so the
            rest of the flow works without Azure or file storage.

────────────────────────────────────────────────────────────────────────────
## 1.5️⃣  AI FEATURES FOR WOW EFFECT  🤖✨
────────────────────────────────────────────────────────────────────────────
**Goal**: Ship AI features that remove 70% of manual typing and show "magic" in <2 weeks dev

| **Phase** | **Ship these AI bits first** | **Why** |
|-----------|------------------------------|---------|
| **Prototype (local)** | **Snap-&-Sell** (with stubbed JSON)<br/>**Voice Ledger** (device mic → GPT)<br/>**Reorder Radar** (simple moving average) | They remove 70% of manual typing and show "magic" in <2 weeks dev. |

### 🔥 Priority AI Features:
- **📸 Snap-&-Sell**: Take photo of receipt → auto-parse sale data (stubbed for now)
- **🎤 Voice Ledger**: "Add 5 pairs Nike sneakers at ₹2500 each" → voice-to-sale entry
- **🔮 Reorder Radar**: Smart alerts based on sales velocity and stock trends

────────────────────────────────────────────────────────────────────────────
## 2️⃣  FUNCTIONAL SCOPE  (MVP)
────────────────────────────────────────────────────────────────────────────
■ **Inventory + Reorder Radar** 🔮
   - Add / edit product types (configurable for business)
     • fields: Id, Name, Category (Leather Types, Shoe Types, Clothing, etc), 
       Size/Specifications, Grade, Color, CostPerUnit, CurrentQuantity, UnitOfMeasure
   - Receive stock → increases qty & records CostPerUnit (can vary per batch)  
   - 🔮 **Smart reorder alerts** with AI-powered suggestions based on sales velocity

■ **Sales + AI Magic** 🤖✨
   - 📸 **Snap-&-Sell**: Photo receipt → auto-parsed sale (stubbed)
   - 🎤 **Voice Ledger**: "Add 5 pairs Nike size 42 at ₹2500 each" → instant entry
   - ✋ **Manual sale form** (traditional backup)
     • choose Product Type, quantity/units, SalePricePerUnit
     • auto-compute revenue, cost, profit
   - Smart sales history with AI-source indicators

■ **Metadata for Product Variants**
   - Separate table **ProductMeta** with flexible key/value pairs OR
     a JSON column in Product table (your choice).  Examples:
     `{ "IdealFor": ["Formal", "Casual"], "Material": "Genuine Leather", "Size": "42" }`
     `{ "Finish": "Nappa", "Thickness": "2.5mm", "Uses": ["Shoes", "Belts"] }`

■ **Reports & Analytics Dashboard** 📊💰
   - **📈 Interactive Dashboard**: Today's Sales ₹, Profit ₹, Low-Stock alerts with trend graphs
   - **💹 Sales Analytics**: 
     * Revenue trends (daily/weekly/monthly) with comparison charts
     * Top-selling products with visual rankings
     * Profit margin analysis by product category
     * Customer purchase patterns and frequency analysis
   - **📦 Inventory Intelligence**:
     * Stock aging reports with color-coded alerts
     * Inventory turnover rates and velocity charts  
     * Reorder forecasting with demand prediction graphs
     * Dead stock identification with financial impact
   - **💰 Financial Reports**:
     * P&L statements with visual breakdowns
     * Cash flow tracking and projections
     * Cost analysis (COGS, markup percentages)
     * Tax summaries and GST calculations
   - **📊 Interactive Charts**: Line graphs, bar charts, pie charts, heat maps
   - **📥 Export Everything**: CSV, PDF, Excel with custom date ranges and filters
   - **🔍 Advanced Filters**: By date, product, category, supplier, profitability

────────────────────────────────────────────────────────────────────────────
## 3️⃣  DETAILED TASK LIST  (Claude executes sequentially)
────────────────────────────────────────────────────────────────────────────

### ✅ TASK 1 – Solution Setup & Project Structure [COMPLETED]
**Goal**: Create clean architecture Blazor WASM solution with proper project structure

1.1  **Create Solution & Core Projects**
     - Create new Blazor WASM Hosted solution named `RetailInventory`
     - Add Class Library projects: `RetailInventory.Domain`, `RetailInventory.Infrastructure`, `RetailInventory.Application`
     - Verify default projects: `RetailInventory.Server` (WebAPI), `RetailInventory.Client` (Blazor WASM)
     - Set up proper project references (Domain → Infrastructure → Application → Server/Client)

1.2  **Configure Project Dependencies**
     - Add EF Core packages to Infrastructure project
     - Add MediatR and FluentValidation packages to Application project
     - Add MudBlazor package to Client project
     - Configure shared DTOs in a Shared project or namespace

1.3  **Folder Structure Setup**
     - Domain: Entities, Enums, Interfaces, ValueObjects
     - Infrastructure: DbContext, Repositories, Configurations
     - Application: Commands, Queries, DTOs, Validators, Services
     - Server: Controllers, Program.cs configuration
     - Client: Pages, Components, Services

**Acceptance Criteria**: ✅ Solution builds successfully, all projects reference correctly

### ✅ TASK 2 – Domain Model & Database Setup [COMPLETED]
**Goal**: Define complete domain model with EF Core configuration and database creation

2.1  **Define Core Entities**
     - `Product` entity: Id, Name, Category (enum), Specifications (size/dimensions), Grade, Color, CurrentQuantity, ReorderThreshold, UnitOfMeasure, IsActive
     - `StockBatch` entity: Id, ProductId, CostPerUnit, Quantity, ReceivedDate, BatchNumber, SupplierId (optional)
     - `Sale` entity: Id, SaleDate, TotalAmount, TotalCost, TotalProfit, CustomerId (optional), Notes
     - `SaleLine` entity: Id, SaleId, ProductId, Quantity, SalePricePerUnit, CostPerUnit, LineTotal, LineProfit
     - `ProductMeta` entity: Id, ProductId, MetadataJson (JSON column for flexible key-value pairs)

2.2  **Define Enums and Value Objects**
     - `ProductCategory` enum: Leather, Footwear, Clothing, Accessories, Materials
     - `UnitOfMeasure` enum: Kilograms, Pieces, Pairs, Meters, Liters
     - Money value object for currency handling
     - Quantity value object with unit of measure

2.3  **EF Core Configuration**
     - Create `RetailInventoryDbContext` with DbSets for all entities
     - Configure entity relationships and constraints using Fluent API
     - Set up connection string for SQL Server with Integrated Authentication
     - Configure JSON column for ProductMeta.MetadataJson
     - Add audit fields (CreatedAt, UpdatedAt) to relevant entities

2.4  **Database Migration & Seed Data (Shoe Shop Focus)**
     - Create initial EF migration with all tables and relationships
     - Create seed data configuration for **Shoe Shop Business**:
       * **Footwear Products**:
         - Nike Air Max (Size 8-12, Black/White/Red) - ₹4500/pair
         - Adidas Ultraboost (Size 7-11, Black/Blue/Grey) - ₹6200/pair  
         - Formal Oxford Shoes (Size 6-12, Brown/Black) - ₹3200/pair
         - Women's Heels (Size 5-9, Red/Black/Nude) - ₹2800/pair
         - Casual Sneakers (Size 6-11, Various Colors) - ₹1800/pair
         - Sports Running Shoes (Size 7-12, Multi-color) - ₹3500/pair
       * **Stock Batches**: 2-3 batches per shoe type with varying costs and sizes
       * **Sample Sales**: 5 realistic shoe sales with multiple pairs and sizes
       * **Product Metadata Examples**:
         - `{"Brand": "Nike", "Size": "10", "Color": "Black", "Material": "Mesh/Rubber", "Type": "Running"}`
         - `{"Brand": "Adidas", "Size": "9", "Color": "White", "Material": "Knit/Boost", "Type": "Lifestyle"}`
         - `{"Brand": "Clarks", "Size": "8", "Color": "Brown", "Material": "Leather", "Type": "Formal"}`
     - Run database update command

**Acceptance Criteria**: ✅ Database created successfully, seed data populated, all relationships working

### ✅ TASK 3 – API Layer with MediatR Pattern [COMPLETED]
**Goal**: Implement clean API endpoints using MediatR and CQRS pattern

3.1  **Setup MediatR Infrastructure**
     - Configure MediatR in Program.cs with assembly scanning
     - Create base Command and Query classes
     - Set up FluentValidation pipeline behavior
     - Configure AutoMapper for entity-to-DTO mapping

3.2  **Product Management APIs**
     - `GET /api/products` - Query to get all products with current stock
     - `GET /api/products/{id}` - Query to get specific product details
     - `POST /api/products` - Command to create new product
     - `PUT /api/products/{id}` - Command to update product details
     - `DELETE /api/products/{id}` - Command to soft delete product

3.3  **Stock Management APIs**
     - `POST /api/stock/receive` - Command to add new stock batch
     - `GET /api/stock/batches/{productId}` - Query to get stock batches for product
     - `GET /api/stock/low-stock` - Query to get items below reorder threshold

3.4  **Sales Management APIs**
     - `POST /api/sales/manual` - Command to create manual sale
     - `POST /api/sales/from-receipt` - Command to create sale from receipt parsing
     - `GET /api/sales` - Query to get sales with pagination and filtering
     - `GET /api/sales/{id}` - Query to get specific sale details

3.5  **Reporting APIs**
     - `GET /api/reports/dashboard` - Query for dashboard metrics (today's sales, profit, low stock count)
     - `GET /api/reports/sales-summary` - Query for sales summary by date range
     - `GET /api/reports/inventory-valuation` - Query for current inventory value

**Acceptance Criteria**: ✅ All APIs working, proper validation, error handling, and response formatting

### ✅ TASK 4 – AI-Powered Features (Stubbed for Wow Effect) [COMPLETED]
**Goal**: Create AI features that eliminate 70% of manual typing and demonstrate "magic"

4.1  **📸 Snap-&-Sell Receipt Parser**
     - Create `IReceiptParserService` interface
     - Define `ParsedReceiptDto` with fields: ProductType, Quantity, UnitPrice, Date, Confidence
     - Create `ReceiptParsingException` for error handling
     - Implement `StubReceiptParserService` with realistic fake data
     - Simulate camera capture UI with instant parsing results

4.2  **🎤 Voice Ledger Service (Speech-to-Sale)**
     - Create `IVoiceLedgerService` interface for voice commands
     - Define voice command patterns: "Add [quantity] [unit] [product] at [price] per [unit]"
     - Implement `StubVoiceLedgerService` that parses text commands
     - Create voice input UI component with microphone icon
     - Return structured sale data from voice commands
     - Examples: "Add 5 pairs Nike sneakers size 42 at 2500 rupees each"
                 "Add 3 kg brown leather at 800 rupees per kg"

4.3  **🔮 Reorder Radar (Smart Alerts)**
     - Create `IReorderRadarService` interface
     - Implement simple moving average algorithm for sales velocity
     - Calculate optimal reorder points based on:
       * Average daily sales (last 30 days)
       * Current stock levels
       * Lead time estimates (configurable)
     - Generate smart reorder suggestions with quantities
     - Show trend indicators (↗️ increasing demand, ↘️ decreasing)

4.4  **Integration with Sales & Inventory APIs**
     - Wire all AI services into DI container
     - Integrate Snap-&-Sell with `/api/sales/from-receipt` endpoint
     - Add `/api/sales/voice-command` endpoint for Voice Ledger
     - Add `/api/inventory/reorder-suggestions` endpoint for Radar
     - Handle AI service failures gracefully with fallbacks

**Acceptance Criteria**: ✅ All AI features work with realistic stub data, users experience "magic" automation

### 🎨 TASK 5 – Blazor UI with MudBlazor [IN PROGRESS]
**Goal**: Create responsive, mobile-first UI for all core functionality

5.1  **Setup MudBlazor and Base Layout**
     - Configure MudBlazor in Program.cs and _Imports.razor
     - Create responsive layout with navigation drawer
     - Set up theme configuration with modern retail industry colors
     - Configure toast notifications and dialog services

5.2  **Dashboard Page with AI Insights**
     - Today's sales revenue and profit cards
     - 🔮 **Reorder Radar alerts** with smart suggestions
     - Quick action buttons (Add Sale, Receive Stock, 📸 Snap Sale, 🎤 Voice Entry)
     - Recent sales table with AI-parsed indicators
     - Inventory value summary with trend predictions

5.3  **AI-Powered Sales Entry**
     - 📸 **Snap-&-Sell component**: Camera capture → instant receipt parsing
     - 🎤 **Voice Ledger widget**: Microphone button → voice-to-sale conversion
     - Manual sale form with product selection and quantity input (fallback)
     - Receipt import page with preview functionality
     - Sales history with AI-source indicators (📸/🎤/✋ manual)

5.4  **Product Management Pages**
     - Product list page with search, filter, and sort by category
     - Add/Edit product form with validation (dynamic fields based on category)
     - Product details page with stock history
     - Product metadata management interface (flexible key-value pairs)

5.4  **Stock Management Pages**
     - Receive stock form with batch tracking
     - Stock batches list with FIFO/LIFO indicators
     - Stock movement history
     - Reorder threshold management

5.5  **Sales Management Pages**
     - Manual sale form with product selection and quantity/unit input
     - Receipt import page with preview functionality
     - Sales history with filtering and search
     - Sale details page with line items

5.6  **Mobile Responsiveness**
     - Ensure all forms work on mobile devices
     - Implement touch-friendly controls
     - Optimize table layouts for small screens
     - Add swipe gestures where appropriate

**Acceptance Criteria**: All pages functional, responsive, and user-friendly on mobile and desktop

### ⚙️ TASK 6 – Advanced Reporting & Analytics Dashboard
**Goal**: Create comprehensive reporting system with interactive charts that provide massive business value

6.1  **Chart.js Integration & Visualization Framework**
     - Add Chart.js or ApexCharts to Blazor Client project
     - Create reusable chart components (LineChart, BarChart, PieChart, DonutChart)
     - Implement responsive charts that work on mobile and desktop
     - Add interactive features (drill-down, filtering, zooming)
     - Create chart color themes matching business branding

6.2  **Sales Analytics & Performance Reports**
     - **📈 Sales Trend Analysis**:
       * Daily/Weekly/Monthly revenue trends with YoY comparison
       * Sales velocity charts showing peak hours and seasons
       * Product performance rankings with visual bars
       * Customer purchase frequency heat maps
     - **💰 Profitability Dashboard**:
       * Gross margin analysis by product category (pie charts)
       * Profit trend lines with target vs actual comparisons
       * Top 10 most/least profitable products (horizontal bar charts)
       * Break-even analysis with visual indicators
     - **🎯 KPI Scorecards**:
       * Average order value trends
       * Sales conversion rates
       * Inventory turnover ratios
       * Customer retention metrics

6.3  **Inventory Intelligence & Stock Analytics**
     - **📦 Stock Level Monitoring**:
       * Real-time inventory valuation with trend indicators
       * Stock aging analysis (30/60/90+ days) with color coding
       * Fast/slow-moving inventory classification charts
       * Dead stock alerts with financial impact calculations
     - **🔮 Demand Forecasting**:
       * Sales velocity predictions using moving averages
       * Seasonal demand patterns (line charts with season overlays)
       * Reorder point optimization with lead time analysis
       * Stock-out risk assessments with probability indicators
     - **📊 Inventory Health Reports**:
       * ABC analysis (80/20 rule) for product importance
       * Inventory turnover rate comparisons by category
       * Carrying cost analysis and optimization suggestions
       * Supplier performance scorecards

6.4  **Financial Reports & P&L Analytics**
     - **💹 Financial Dashboard**:
       * Revenue vs Cost breakdown with waterfall charts
       * Monthly P&L statements with visual comparisons
       * Cash flow projections and trend analysis
       * GST/Tax summaries with compliance tracking
     - **💸 Cost Analysis**:
       * COGS (Cost of Goods Sold) trends by product
       * Markup percentage analysis across categories
       * Supplier cost comparison charts
       * Price elasticity analysis for optimization

6.5  **Interactive Analytics & Drill-Down Reports**
     - **🔍 Advanced Filtering System**:
       * Date range pickers with preset options (Last 7/30/90 days)
       * Multi-select product/category filters
       * Supplier and brand filtering
       * Profitability range filters (High/Medium/Low margin)
     - **📊 Cross-Tab Analysis**:
       * Sales by Product Category vs Time Period
       * Profitability by Supplier vs Product Type
       * Customer segments vs Purchase patterns
       * Seasonal trends vs Product performance

6.6  **Export & Sharing Capabilities**
     - **📥 Multi-Format Export**:
       * CSV exports with customizable columns
       * PDF reports with branded templates and charts
       * Excel files with multiple sheets and formulas
       * Image exports of charts for presentations
     - **📧 Automated Reporting**:
       * Scheduled email reports (daily/weekly/monthly)
       * Alert notifications for low stock, high sales, etc.
       * Executive summary reports with key insights
       * Custom report builders for specific business needs

6.7  **Mobile-Optimized Analytics**
     - **📱 Mobile Dashboard**:
       * Touch-friendly charts and navigation
       * Swipe gestures for time period navigation
       * Voice-activated report requests
       * Offline report caching for key metrics
     - **🔔 Real-Time Notifications**:
       * Push notifications for important alerts
       * Real-time dashboard updates
       * Goal achievement celebrations
       * Anomaly detection alerts

**Acceptance Criteria**: 
- All reports load in under 2 seconds with 10,000+ records
- Charts are interactive and mobile-responsive
- Export functions work flawlessly in all formats  
- Users can create custom date ranges and filter combinations
- Visual insights provide clear business value and actionable data

### 🧪 TASK 7 – Unit Testing
**Goal**: Ensure code quality with comprehensive unit tests

7.1  **Domain Logic Tests**
     - Test stock deduction logic with FIFO/LIFO
     - Test profit calculation accuracy
     - Test business rule validations
     - Test entity behavior and invariants

7.2  **Application Layer Tests**
     - Test MediatR command and query handlers
     - Test validation rules with FluentValidation
     - Test AutoMapper configurations
     - Mock external dependencies

7.3  **API Integration Tests**
     - Test API endpoints with in-memory database
     - Test authentication and authorization (when added)
     - Test error handling and response formats
     - Test data validation and business rules

7.4  **Service Tests**
     - Test receipt parser service behavior
     - Test CSV export service functionality
     - Test calculation services accuracy
     - Mock database interactions

**Acceptance Criteria**: 80%+ code coverage, all tests passing, CI/CD ready

### 🔄 TASK 8 – Documentation & Deployment Setup
**Goal**: Complete project with documentation and easy local setup

8.1  **README Documentation**
     - Prerequisites: .NET 8 SDK, SQL Server/LocalDB setup
     - Step-by-step installation instructions
     - Database setup and migration commands
     - Running the application locally
     - API documentation links
     - Troubleshooting common issues

8.2  **Development Setup Scripts**
     - PowerShell script for initial setup
     - Database creation and migration script
     - Sample data loading script
     - Development environment validation script

8.3  **Configuration Management**
     - Environment-specific appsettings files
     - Connection string configuration examples
     - **Business Type Configuration**: Easy setup for different retail types
       * Leather Shop: Categories (Full-Grain, Suede, Nappa), Units (kg, sq meters)
       * Shoe Store: Categories (Formal, Casual, Sports), Units (pairs), Sizes
       * Clothing Store: Categories (Shirts, Pants, Dresses), Units (pieces), Sizes
     - Logging configuration for development/production
     - Feature flags for optional functionality

8.4  **Launch Configuration**
     - Configure launchSettings.json for multiple startup projects
     - Set up proper CORS policies
     - Configure development certificates
     - Set up hot reload for efficient development

**Acceptance Criteria**: New developer can set up and run the project in under 30 minutes

────────────────────────────────────────────────────────────────────────────
## 4️⃣  EXECUTION GUIDELINES  (for Claude)
────────────────────────────────────────────────────────────────────────────
• Present each sub-task under an `### ✅ TASK X.Y` header.
• Include the **actual code** (C# classes, migrations, razor pages, etc.).
• For long code blocks, use triple backticks and proper language tags.

────────────────────────────────────────────────────────────────────────────
## 5️⃣  OPTIONAL QUESTIONS BEFORE START
────────────────────────────────────────────────────────────────────────────
1. Do we need **unit of measure** other than kg  - yes (Number of units for shoes etc))
2. Should we include **GST fields** now or later?  (now)
3. Is **English-only UI** fine for the prototype?  (yes)
4. Any preference between **MudBlazor** and **Radzen**? You analyze and decide

If answers are unclear, assume: only kg, skip GST for now, English-only, use MudBlazor.

Begin with **TASK 1 – Solution Setup**.

────────────────────────────────────────────────────────────────────────────
## 📋 TASK EXECUTION CHECKLIST
────────────────────────────────────────────────────────────────────────────

Each task should be marked as complete only when:
- [ ] All subtasks are implemented with working code
- [ ] Code compiles without errors or warnings
- [ ] Basic functionality is tested and working
- [ ] Acceptance criteria are met
- [ ] Documentation is updated if needed

**Current Status**: Ready to begin TASK 1 - Solution Setup & Project Structure
