@page "/sales/history"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Sales History - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <div class="d-flex justify-space-between align-center mb-4">
        <div>
            <MudText Typo="Typo.h4">📈 Sales History</MudText>
            <MudText Typo="Typo.body1" Color="Color.Default">
                Track and analyze your sales performance
            </MudText>
        </div>
        <MudButton Variant="Variant.Filled" 
                   Color="Color.Primary" 
                   StartIcon="@Icons.Material.Filled.Add"
                   Href="/sales/manual">
            New Sale
        </MudButton>
    </div>

    <!-- Filters -->
    <MudCard Elevation="2" Class="mb-4">
        <MudCardContent>
            <MudGrid>
                <MudItem xs="12" md="3">
                    <MudDateRangePicker @bind-DateRange="_dateRange" 
                                        Label="Date Range" 
                                        Variant="Variant.Outlined"
                                        OnSelectionChanged="OnDateRangeChanged" />
                </MudItem>
                <MudItem xs="12" md="2">
                    <MudSelect @bind-Value="_sourceFilter" 
                               Label="Source" 
                               Variant="Variant.Outlined"
                               T="string"
                               Clearable="true"
                               OnSelectionChanged="OnSourceFilterChanged">
                        <MudSelectItem Value="@("Manual")">✋ Manual</MudSelectItem>
                        <MudSelectItem Value="@("Voice")">🎤 Voice</MudSelectItem>
                        <MudSelectItem Value="@("Receipt")">📸 Receipt</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="2">
                    <MudNumericField @bind-Value="_minAmountFilter" 
                                     Label="Min Amount (₹)" 
                                     Variant="Variant.Outlined"
                                     Min="0"
                                     OnValueChanged="OnAmountFilterChanged" />
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudTextField @bind-Value="_searchText" 
                                  Label="Search (Customer/Notes)" 
                                  Variant="Variant.Outlined"
                                  Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Search"
                                  OnKeyUp="OnSearchKeyUp" />
                </MudItem>
                <MudItem xs="12" md="2">
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Secondary" 
                               FullWidth="true"
                               OnClick="ClearFilters">
                        Clear Filters
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- Summary Cards -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.Receipt" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
                        <div>
                            <MudText Typo="Typo.h4">@_totalSales</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Default">Total Sales</MudText>
                        </div>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Color="Color.Success" Size="Size.Large" Class="mr-3" />
                        <div>
                            <MudText Typo="Typo.h4">₹@_totalRevenue.ToString("N0")</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Default">Total Revenue</MudText>
                        </div>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Info" Size="Size.Large" Class="mr-3" />
                        <div>
                            <MudText Typo="Typo.h4">₹@_totalProfit.ToString("N0")</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Default">Total Profit</MudText>
                        </div>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4">
                <MudCardContent Class="pa-0">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.Percent" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                        <div>
                            <MudText Typo="Typo.h4">@_avgProfitMargin.ToString("N1")%</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Default">Avg Margin</MudText>
                        </div>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- Sales Table -->
    <MudCard Elevation="2">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">📋 Sales Records</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           StartIcon="@Icons.Material.Filled.FileDownload"
                           OnClick="ExportSales">
                    Export
                </MudButton>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent Class="pa-0">
            @if (_loading)
            {
                <div class="d-flex justify-center pa-4">
                    <MudProgressCircular Indeterminate="true" />
                </div>
            }
            else if (_filteredSales?.Any() == true)
            {
                <MudTable Items="_filteredSales" 
                          Hover="true" 
                          Striped="true" 
                          Dense="true"
                          FixedHeader="true"
                          Height="600px">
                    <HeaderContent>
                        <MudTh>Sale ID</MudTh>
                        <MudTh>Date</MudTh>
                        <MudTh>Source</MudTh>
                        <MudTh>Items</MudTh>
                        <MudTh>Amount</MudTh>
                        <MudTh>Profit</MudTh>
                        <MudTh>Margin</MudTh>
                        <MudTh>Customer</MudTh>
                        <MudTh>Actions</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>
                            <MudText Typo="Typo.body1"><strong>#@context.Id.ToString("D4")</strong></MudText>
                        </MudTd>
                        <MudTd>
                            <div>
                                <MudText Typo="Typo.body2">@context.SaleDate.ToString("MMM dd, yyyy")</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Default">@context.SaleDate.ToString("HH:mm")</MudText>
                            </div>
                        </MudTd>
                        <MudTd>
                            <MudChip Size="Size.Small" Color="GetSourceColor(context.Source)" Icon="@GetSourceIcon(context.Source)">
                                @context.Source
                            </MudChip>
                        </MudTd>
                        <MudTd>@context.TotalItemsSold items</MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body1"><strong>₹@context.TotalAmount.ToString("N2")</strong></MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body1" Color="@(context.TotalProfit >= 0 ? Color.Success : Color.Error)">
                                <strong>₹@context.TotalProfit.ToString("N2")</strong>
                            </MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2" Color="@(context.ProfitMargin >= 0 ? Color.Success : Color.Error)">
                                @context.ProfitMargin.ToString("N1")%
                            </MudText>
                        </MudTd>
                        <MudTd>
                            @if (!string.IsNullOrEmpty(context.CustomerName))
                            {
                                <MudText Typo="Typo.body2">@context.CustomerName</MudText>
                            }
                            else
                            {
                                <MudText Typo="Typo.caption" Color="Color.Default">Walk-in</MudText>
                            }
                        </MudTd>
                        <MudTd>
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                               Color="Color.Primary" 
                                               Size="Size.Small"
                                               OnClick="() => ViewSale(context.Id)"
                                               Title="View Details" />
                                <MudIconButton Icon="@Icons.Material.Filled.Print" 
                                               Color="Color.Secondary" 
                                               Size="Size.Small"
                                               OnClick="() => PrintReceipt(context.Id)"
                                               Title="Print Receipt" />
                                <MudIconButton Icon="@Icons.Material.Filled.Undo" 
                                               Color="Color.Warning" 
                                               Size="Size.Small"
                                               OnClick="() => RefundSale(context.Id)"
                                               Title="Refund Sale" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            }
            else
            {
                <div class="d-flex flex-column align-center pa-8">
                    <MudIcon Icon="@Icons.Material.Filled.Receipt" Size="Size.Large" Color="Color.Default" Class="mb-4" />
                    <MudText Typo="Typo.h6" Color="Color.Default">No sales found</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Default" Class="mb-4">
                        @if (HasActiveFilters())
                        {
                            <span>Try adjusting your filters or date range.</span>
                        }
                        else
                        {
                            <span>Start making sales to see them here.</span>
                        }
                    </MudText>
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Primary" 
                               StartIcon="@Icons.Material.Filled.Add"
                               Href="/sales/manual">
                        Create First Sale
                    </MudButton>
                </div>
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private List<SaleDto>? _sales;
    private List<SaleDto> _filteredSales = new();
    private bool _loading = true;
    private DateRange? _dateRange = new DateRange(DateTime.Today.AddDays(-30), DateTime.Today);
    private string? _sourceFilter;
    private decimal? _minAmountFilter;
    private string _searchText = "";

    private int _totalSales => _filteredSales?.Count ?? 0;
    private decimal _totalRevenue => _filteredSales?.Sum(s => s.TotalAmount) ?? 0;
    private decimal _totalProfit => _filteredSales?.Sum(s => s.TotalProfit) ?? 0;
    private decimal _avgProfitMargin => _filteredSales?.Any() == true ? _filteredSales.Average(s => s.ProfitMargin) : 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadSales();
    }

    private async Task LoadSales()
    {
        try
        {
            _loading = true;
            var response = await Http.GetFromJsonAsync<List<SaleDto>>("api/sales");
            _sales = response ?? new List<SaleDto>();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading sales: {ex.Message}", Severity.Error);
            _sales = new List<SaleDto>();
        }
        finally
        {
            _loading = false;
        }
    }

    private void ApplyFilters()
    {
        if (_sales == null)
        {
            _filteredSales = new List<SaleDto>();
            return;
        }

        var filtered = _sales.AsEnumerable();

        // Date range filter
        if (_dateRange?.Start.HasValue == true && _dateRange?.End.HasValue == true)
        {
            filtered = filtered.Where(s => s.SaleDate.Date >= _dateRange.Start.Value.Date && 
                                          s.SaleDate.Date <= _dateRange.End.Value.Date);
        }

        // Source filter
        if (!string.IsNullOrEmpty(_sourceFilter))
        {
            filtered = filtered.Where(s => s.Source.Equals(_sourceFilter, StringComparison.OrdinalIgnoreCase));
        }

        // Amount filter
        if (_minAmountFilter.HasValue)
        {
            filtered = filtered.Where(s => s.TotalAmount >= _minAmountFilter.Value);
        }

        // Search filter
        if (!string.IsNullOrWhiteSpace(_searchText))
        {
            filtered = filtered.Where(s => 
                (s.CustomerName?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) == true) ||
                (s.Notes?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) == true));
        }

        _filteredSales = filtered.OrderByDescending(s => s.SaleDate).ToList();
    }

    private void OnDateRangeChanged(DateRange? dateRange)
    {
        _dateRange = dateRange;
        ApplyFilters();
    }

    private void OnSourceFilterChanged(string? source)
    {
        _sourceFilter = source;
        ApplyFilters();
    }

    private void OnAmountFilterChanged(decimal? amount)
    {
        _minAmountFilter = amount;
        ApplyFilters();
    }

    private async Task OnSearchKeyUp(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            ApplyFilters();
        }
    }

    private void ClearFilters()
    {
        _dateRange = new DateRange(DateTime.Today.AddDays(-30), DateTime.Today);
        _sourceFilter = null;
        _minAmountFilter = null;
        _searchText = "";
        ApplyFilters();
    }

    private bool HasActiveFilters()
    {
        return !string.IsNullOrWhiteSpace(_searchText) || 
               !string.IsNullOrEmpty(_sourceFilter) || 
               _minAmountFilter.HasValue;
    }

    private Color GetSourceColor(string source)
    {
        return source.ToLowerInvariant() switch
        {
            "manual" => Color.Primary,
            "voice" => Color.Secondary,
            "receipt" => Color.Tertiary,
            _ => Color.Default
        };
    }

    private string GetSourceIcon(string source)
    {
        return source.ToLowerInvariant() switch
        {
            "manual" => Icons.Material.Filled.TouchApp,
            "voice" => Icons.Material.Filled.Mic,
            "receipt" => Icons.Material.Filled.CameraAlt,
            _ => Icons.Material.Filled.Receipt
        };
    }

    private void ViewSale(int saleId)
    {
        Navigation.NavigateTo($"/sales/{saleId}");
    }

    private void PrintReceipt(int saleId)
    {
        Snackbar.Add("Print functionality coming soon!", Severity.Info);
    }

    private void RefundSale(int saleId)
    {
        Snackbar.Add("Refund functionality coming soon!", Severity.Info);
    }

    private void ExportSales()
    {
        Snackbar.Add("Export functionality coming soon!", Severity.Info);
    }

    // Temporary SaleDto class - this should match the one from the API
    public class SaleDto
    {
        public int Id { get; set; }
        public DateTime SaleDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public string CustomerName { get; set; } = "";
        public string Notes { get; set; } = "";
        public string Source { get; set; } = "";
        public int TotalItemsSold { get; set; }
    }
}
