@page "/products/add"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Add Product - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-4">
    <div class="d-flex align-center mb-4">
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" 
                       Color="Color.Default" 
                       OnClick="GoBack" />
        <MudText Typo="Typo.h4" Class="ml-2">Add New Product</MudText>
    </div>

    <MudCard Elevation="3">
        <MudCardContent>
            <EditForm Model="_productDto" OnValidSubmit="HandleSubmit">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <!-- Basic Information -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-3">📋 Basic Information</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_productDto.Name"
                                      Label="Product Name"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      For="@(() => _productDto.Name)"
                                      HelperText="Enter the product name" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudSelect @bind-Value="_productDto.Category"
                                   Label="Category"
                                   Variant="Variant.Outlined"
                                   Required="true"
                                   For="@(() => _productDto.Category)">
                            <MudSelectItem Value="ProductCategory.Footwear">👟 Footwear</MudSelectItem>
                            <MudSelectItem Value="ProductCategory.Leather">🧳 Leather</MudSelectItem>
                            <MudSelectItem Value="ProductCategory.Clothing">👕 Clothing</MudSelectItem>
                            <MudSelectItem Value="ProductCategory.Accessories">👜 Accessories</MudSelectItem>
                            <MudSelectItem Value="ProductCategory.Materials">🧵 Materials</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_productDto.Specifications"
                                      Label="Specifications"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      For="@(() => _productDto.Specifications)"
                                      HelperText="e.g., Size 8-12, 2.5mm thickness" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_productDto.Grade"
                                      Label="Grade/Quality"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      For="@(() => _productDto.Grade)"
                                      HelperText="e.g., Premium, Standard, Grade A" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_productDto.Color"
                                      Label="Color"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      For="@(() => _productDto.Color)"
                                      HelperText="e.g., Black, Brown, Multi-color" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudSelect @bind-Value="_productDto.UnitOfMeasure"
                                   Label="Unit of Measure"
                                   Variant="Variant.Outlined"
                                   Required="true"
                                   For="@(() => _productDto.UnitOfMeasure)">
                            <MudSelectItem Value="UnitOfMeasure.Pairs">👟 Pairs</MudSelectItem>
                            <MudSelectItem Value="UnitOfMeasure.Pieces">📦 Pieces</MudSelectItem>
                            <MudSelectItem Value="UnitOfMeasure.Kilograms">⚖️ Kilograms</MudSelectItem>
                            <MudSelectItem Value="UnitOfMeasure.Meters">📏 Meters</MudSelectItem>
                            <MudSelectItem Value="UnitOfMeasure.Liters">🥤 Liters</MudSelectItem>
                        </MudSelect>
                    </MudItem>

                    <!-- Inventory Settings -->
                    <MudItem xs="12" Class="mt-4">
                        <MudText Typo="Typo.h6" Class="mb-3">📊 Inventory Settings</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudNumericField @bind-Value="_productDto.ReorderThreshold"
                                         Label="Reorder Threshold"
                                         Variant="Variant.Outlined"
                                         Min="0"
                                         Required="true"
                                         For="@(() => _productDto.ReorderThreshold)"
                                         HelperText="Alert when stock falls below this level" />
                    </MudItem>

                    <!-- Product Metadata -->
                    <MudItem xs="12" Class="mt-4">
                        <MudText Typo="Typo.h6" Class="mb-3">🏷️ Additional Metadata (Optional)</MudText>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="_metadataJson"
                                      Label="Metadata (JSON)"
                                      Variant="Variant.Outlined"
                                      Lines="4"
                                      HelperText="Enter additional product information as JSON"
                                      Placeholder="{&quot;Brand&quot;: &quot;Nike&quot;, &quot;Material&quot;: &quot;Mesh/Rubber&quot;, &quot;Type&quot;: &quot;Running&quot;}" />
                    </MudItem>

                    <!-- Action Buttons -->
                    <MudItem xs="12" Class="mt-6">
                        <div class="d-flex justify-space-between">
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Default"
                                       StartIcon="@Icons.Material.Filled.Cancel"
                                       OnClick="GoBack">
                                Cancel
                            </MudButton>
                            
                            <MudButton ButtonType="ButtonType.Submit"
                                       Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       Disabled="_isSubmitting">
                                @if (_isSubmitting)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                    <span>Creating Product...</span>
                                }
                                else
                                {
                                    <span>Create Product</span>
                                }
                            </MudButton>
                        </div>
                    </MudItem>
                </MudGrid>
            </EditForm>
        </MudCardContent>
    </MudCard>

    <!-- Help Card -->
    <MudCard Elevation="2" Class="mt-4">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">💡 Tips for Adding Products</MudText>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            <MudList Dense="true">
                <MudListItem Icon="@Icons.Material.Filled.TipsAndUpdates" IconColor="Color.Info">
                    <MudText Typo="Typo.body2">Use descriptive names that include key details like brand or type</MudText>
                </MudListItem>
                <MudListItem Icon="@Icons.Material.Filled.Category" IconColor="Color.Secondary">
                    <MudText Typo="Typo.body2">Choose the most appropriate category for better organization</MudText>
                </MudListItem>
                <MudListItem Icon="@Icons.Material.Filled.Inventory" IconColor="Color.Warning">
                    <MudText Typo="Typo.body2">Set reorder thresholds based on your typical sales volume</MudText>
                </MudListItem>
                <MudListItem Icon="@Icons.Material.Filled.DataObject" IconColor="Color.Tertiary">
                    <MudText Typo="Typo.body2">Use metadata to store additional information like brand, material, or supplier details</MudText>
                </MudListItem>
            </MudList>
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private CreateProductDto _productDto = new();
    private string _metadataJson = "";
    private bool _isSubmitting = false;

    private async Task HandleSubmit()
    {
        try
        {
            _isSubmitting = true;

            // Validate JSON metadata if provided
            if (!string.IsNullOrWhiteSpace(_metadataJson))
            {
                try
                {
                    System.Text.Json.JsonDocument.Parse(_metadataJson);
                }
                catch
                {
                    Snackbar.Add("Invalid JSON format in metadata field", Severity.Error);
                    return;
                }
            }

            var response = await Http.PostAsJsonAsync("api/products", _productDto);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                var productId = result?.GetProperty("productId").GetInt32() ?? 0;

                // Add metadata if provided
                if (!string.IsNullOrWhiteSpace(_metadataJson) && productId > 0)
                {
                    var metadataDto = new { ProductId = productId, MetadataJson = _metadataJson };
                    await Http.PostAsJsonAsync("api/products/metadata", metadataDto);
                }

                Snackbar.Add("Product created successfully!", Severity.Success);
                Navigation.NavigateTo("/products");
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"Error creating product: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error creating product: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSubmitting = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/products");
    }

    public class CreateProductDto
    {
        public string Name { get; set; } = "";
        public ProductCategory Category { get; set; } = ProductCategory.Footwear;
        public string Specifications { get; set; } = "";
        public string Grade { get; set; } = "";
        public string Color { get; set; } = "";
        public decimal ReorderThreshold { get; set; } = 10;
        public UnitOfMeasure UnitOfMeasure { get; set; } = UnitOfMeasure.Pairs;
    }
}
