@inherits LayoutComponentBase
@inject IJSRuntime JSRuntime

<MudThemeProvider @ref="@_mudThemeProvider" IsDarkMode="@_isDarkMode" />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudSpacer />
        <MudText Typo="Typo.h5" Class="ml-3">Retail Inventory System</MudText>
        <MudSpacer />
        <MudIconButton Icon="@Icons.Material.Filled.Brightness4" Color="Color.Inherit" OnClick="@((e) => DarkModeToggle())" />
        <MudIconButton Icon="@Icons.Material.Filled.Notifications" Color="Color.Inherit" />
    </MudAppBar>
    
    <MudDrawer Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>
    
    <MudMainContent Class="mt-16 pa-4">
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-0">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    private bool _drawerOpen = true;
    private bool _isDarkMode = false;
    private MudThemeProvider _mudThemeProvider = null!;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _isDarkMode = await _mudThemeProvider.GetSystemPreference();
            StateHasChanged();
        }
    }

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void DarkModeToggle()
    {
        _isDarkMode = !_isDarkMode;
    }
}
