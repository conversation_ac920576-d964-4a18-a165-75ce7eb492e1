@page "/sales/receipt"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>Snap & Sell - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h3" GutterBottom="true">📸 Snap & Sell</MudText>
    <MudText Typo="Typo.body1" Class="mb-4">Take a photo of any receipt and watch our AI extract all the details instantly!</MudText>

    <MudGrid>
        <MudItem xs="12" md="8">
            <MudCard Elevation="3" Class="pa-6">
                <MudCardContent Class="pa-0">
                    <!-- Upload Section -->
                    @if (_uploadedImage == null)
                    {
                        <div class="text-center">
                            <MudFileUpload T="IBrowserFile" Accept=".jpg,.jpeg,.png" FilesChanged="OnFileSelected" MaximumFileCount="1">
                                <ButtonTemplate>
                                    <MudButton HtmlTag="label"
                                               Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               Size="Size.Large"
                                               StartIcon="@Icons.Material.Filled.CloudUpload"
                                               for="@context.Id"
                                               Class="pa-6"
                                               Style="min-height: 200px; min-width: 300px; border: 2px dashed #ccc;">
                                        <div>
                                            <MudIcon Icon="@Icons.Material.Filled.CameraAlt" Size="Size.Large" Class="mb-2" />
                                            <br />
                                            <MudText Typo="Typo.h6">Upload Receipt Image</MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Default">
                                                Click to select or drag & drop
                                            </MudText>
                                            <MudText Typo="Typo.caption" Color="Color.Default">
                                                Supports JPG, PNG (Max 5MB)
                                            </MudText>
                                        </div>
                                    </MudButton>
                                </ButtonTemplate>
                            </MudFileUpload>
                        </div>
                    }
                    else
                    {
                        <!-- Image Preview -->
                        <div class="text-center mb-4">
                            <MudImage Src="@_imageDataUrl" 
                                      Alt="Receipt Preview" 
                                      Class="rounded"
                                      Style="max-width: 100%; max-height: 400px;" />
                        </div>
                        
                        <div class="d-flex justify-center gap-2 mb-4">
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Primary" 
                                       StartIcon="@Icons.Material.Filled.AutoAwesome"
                                       OnClick="ProcessReceipt"
                                       Disabled="@_isProcessing">
                                @if (_isProcessing)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                    <span>Processing Receipt...</span>
                                }
                                else
                                {
                                    <span>🤖 Process with AI</span>
                                }
                            </MudButton>
                            
                            <MudButton Variant="Variant.Outlined" 
                                       Color="Color.Secondary" 
                                       StartIcon="@Icons.Material.Filled.Refresh"
                                       OnClick="ResetUpload">
                                Upload Different Image
                            </MudButton>
                        </div>
                    }

                    <!-- Processing Results -->
                    @if (_parseResult != null)
                    {
                        <MudDivider Class="my-4" />
                        
                        @if (_parseResult.IsSuccess)
                        {
                            <MudAlert Severity="Severity.Success" Class="mb-4">
                                <MudText Typo="Typo.h6">✨ Receipt Processed Successfully!</MudText>
                                <MudText Typo="Typo.body2">
                                    Found @_parseResult.ParsedItems.Count items with total value of ₹@_parseResult.TotalAmount.ToString("N0")
                                </MudText>
                            </MudAlert>

                            <MudText Typo="Typo.h6" Class="mb-3">📋 Extracted Items</MudText>
                            
                            <MudTable Items="@_parseResult.ParsedItems" Dense="true" Hover="true" Breakpoint="Breakpoint.Sm">
                                <HeaderContent>
                                    <MudTh>Product</MudTh>
                                    <MudTh>Qty</MudTh>
                                    <MudTh>Unit Price</MudTh>
                                    <MudTh>Total</MudTh>
                                    <MudTh>Confidence</MudTh>
                                    <MudTh>Details</MudTh>
                                </HeaderContent>
                                <RowTemplate>
                                    <MudTd DataLabel="Product">
                                        <div>
                                            <MudText Typo="Typo.body1">@context.ProductType</MudText>
                                            @if (!string.IsNullOrEmpty(context.Brand))
                                            {
                                                <MudChip Size="Size.Small" Color="Color.Primary">@context.Brand</MudChip>
                                            }
                                        </div>
                                    </MudTd>
                                    <MudTd DataLabel="Qty">@context.Quantity</MudTd>
                                    <MudTd DataLabel="Unit Price">₹@context.UnitPrice.ToString("N0")</MudTd>
                                    <MudTd DataLabel="Total">₹@((context.Quantity * context.UnitPrice).ToString("N0"))</MudTd>
                                    <MudTd DataLabel="Confidence">
                                        <MudProgressLinear Color="@GetConfidenceColor(context.Confidence)" 
                                                           Value="@((double)(context.Confidence * 100))" 
                                                           Class="my-1" />
                                        <MudText Typo="Typo.caption">@context.Confidence.ToString("P0")</MudText>
                                    </MudTd>
                                    <MudTd DataLabel="Details">
                                        @if (!string.IsNullOrEmpty(context.Size) || !string.IsNullOrEmpty(context.Color))
                                        {
                                            <div>
                                                @if (!string.IsNullOrEmpty(context.Size))
                                                {
                                                    <MudChip Size="Size.Small" Color="Color.Secondary">Size: @context.Size</MudChip>
                                                }
                                                @if (!string.IsNullOrEmpty(context.Color))
                                                {
                                                    <MudChip Size="Size.Small" Color="Color.Tertiary">@context.Color</MudChip>
                                                }
                                            </div>
                                        }
                                    </MudTd>
                                </RowTemplate>
                            </MudTable>

                            <div class="d-flex justify-space-between align-center mt-4 pa-3" style="background-color: var(--mud-palette-background-grey);">
                                <MudText Typo="Typo.h6">Total Amount: ₹@_parseResult.TotalAmount.ToString("N0")</MudText>
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Success" 
                                           Size="Size.Large"
                                           StartIcon="@Icons.Material.Filled.Save"
                                           OnClick="CreateSalesFromReceipt"
                                           Disabled="@_isCreatingSales">
                                    @if (_isCreatingSales)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                        <span>Creating Sales...</span>
                                    }
                                    else
                                    {
                                        <span>💾 Create Sales Entries</span>
                                    }
                                </MudButton>
                            </div>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Error">
                                <MudText Typo="Typo.h6">❌ Processing Failed</MudText>
                                <MudText Typo="Typo.body2">@_parseResult.ErrorMessage</MudText>
                            </MudAlert>
                        }
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="4">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📋 How It Works</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudTimeline TimelineOrientation="TimelineOrientation.Vertical">
                        <MudTimelineItem Color="Color.Primary" Icon="@Icons.Material.Filled.CameraAlt">
                            <MudText Typo="Typo.h6">1. Upload Receipt</MudText>
                            <MudText Typo="Typo.body2">Take a clear photo of your receipt or upload an existing image</MudText>
                        </MudTimelineItem>
                        <MudTimelineItem Color="Color.Secondary" Icon="@Icons.Material.Filled.AutoAwesome">
                            <MudText Typo="Typo.h6">2. AI Processing</MudText>
                            <MudText Typo="Typo.body2">Our AI extracts product names, quantities, prices, and details</MudText>
                        </MudTimelineItem>
                        <MudTimelineItem Color="Color.Success" Icon="@Icons.Material.Filled.Save">
                            <MudText Typo="Typo.h6">3. Create Sales</MudText>
                            <MudText Typo="Typo.body2">Review and save the extracted data as sales entries</MudText>
                        </MudTimelineItem>
                    </MudTimeline>
                </MudCardContent>
            </MudCard>

            <MudCard Elevation="2" Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">💡 Tips for Best Results</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList Dense="true">
                        <MudListItem Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                            <MudText Typo="Typo.body2">Ensure good lighting</MudText>
                        </MudListItem>
                        <MudListItem Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                            <MudText Typo="Typo.body2">Keep receipt flat and straight</MudText>
                        </MudListItem>
                        <MudListItem Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                            <MudText Typo="Typo.body2">Include all text in the frame</MudText>
                        </MudListItem>
                        <MudListItem Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                            <MudText Typo="Typo.body2">Avoid shadows and glare</MudText>
                        </MudListItem>
                    </MudList>
                </MudCardContent>
            </MudCard>

            <MudCard Elevation="2" Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📊 Processing Stats</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudText Typo="Typo.body2" Class="mb-2">
                        <strong>Receipts Today:</strong> 12
                    </MudText>
                    <MudText Typo="Typo.body2" Class="mb-2">
                        <strong>Success Rate:</strong> 96%
                    </MudText>
                    <MudText Typo="Typo.body2" Class="mb-2">
                        <strong>Items Extracted:</strong> 47
                    </MudText>
                    <MudText Typo="Typo.body2">
                        <strong>Time Saved:</strong> 3.2 hours
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private IBrowserFile? _uploadedImage;
    private string? _imageDataUrl;
    private bool _isProcessing = false;
    private bool _isCreatingSales = false;
    private ReceiptParseResultDto? _parseResult;

    private async Task OnFileSelected(IBrowserFile file)
    {
        try
        {
            if (file.Size > 5 * 1024 * 1024) // 5MB limit
            {
                Snackbar.Add("File size must be less than 5MB", Severity.Error);
                return;
            }

            _uploadedImage = file;

            // Create data URL for preview
            var buffer = new byte[file.Size];
            await file.OpenReadStream().ReadAsync(buffer);
            _imageDataUrl = $"data:{file.ContentType};base64,{Convert.ToBase64String(buffer)}";

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error uploading file: {ex.Message}", Severity.Error);
        }
    }

    private async Task ProcessReceipt()
    {
        if (_uploadedImage == null || string.IsNullOrEmpty(_imageDataUrl))
            return;

        try
        {
            _isProcessing = true;
            StateHasChanged();

            // Convert image to base64
            var buffer = new byte[_uploadedImage.Size];
            await _uploadedImage.OpenReadStream().ReadAsync(buffer);
            var base64Image = Convert.ToBase64String(buffer);

            var receiptUpload = new ReceiptUploadDto
            {
                ImageBase64 = base64Image,
                FileName = _uploadedImage.Name,
                ContentType = _uploadedImage.ContentType
            };

            var response = await Http.PostAsJsonAsync("/api/ai/receipt/parse", receiptUpload);

            if (response.IsSuccessStatusCode)
            {
                _parseResult = await response.Content.ReadFromJsonAsync<ReceiptParseResultDto>();

                if (_parseResult?.IsSuccess == true)
                {
                    Snackbar.Add($"Receipt processed successfully! Found {_parseResult.ParsedItems.Count} items.", Severity.Success);
                }
                else
                {
                    Snackbar.Add(_parseResult?.ErrorMessage ?? "Failed to process receipt", Severity.Warning);
                }
            }
            else
            {
                Snackbar.Add("Failed to connect to AI service", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error processing receipt: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isProcessing = false;
            StateHasChanged();
        }
    }

    private async Task CreateSalesFromReceipt()
    {
        if (_parseResult?.ParsedItems == null || !_parseResult.ParsedItems.Any())
            return;

        try
        {
            _isCreatingSales = true;
            StateHasChanged();

            var receiptUpload = new ReceiptUploadDto
            {
                ImageBase64 = _imageDataUrl?.Split(',')[1] ?? "",
                FileName = _uploadedImage?.Name ?? "receipt.jpg",
                ContentType = _uploadedImage?.ContentType ?? "image/jpeg"
            };

            var response = await Http.PostAsJsonAsync("/api/ai/receipt/create-sale", receiptUpload);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                Snackbar.Add("Sales entries created successfully from receipt!", Severity.Success);

                // Reset form
                await ResetUpload();
            }
            else
            {
                Snackbar.Add("Failed to create sales entries", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error creating sales: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isCreatingSales = false;
            StateHasChanged();
        }
    }

    private async Task ResetUpload()
    {
        _uploadedImage = null;
        _imageDataUrl = null;
        _parseResult = null;
        StateHasChanged();
        await Task.CompletedTask;
    }

    private Color GetConfidenceColor(decimal confidence)
    {
        return confidence switch
        {
            >= 0.9m => Color.Success,
            >= 0.7m => Color.Warning,
            _ => Color.Error
        };
    }

    // DTO classes for API communication
    public class ReceiptUploadDto
    {
        public string ImageBase64 { get; set; } = "";
        public string FileName { get; set; } = "";
        public string ContentType { get; set; } = "";
    }

    public class ReceiptParseResultDto
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = "";
        public List<ParsedReceiptDto> ParsedItems { get; set; } = new();
        public decimal TotalAmount { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
        public string ReceiptId { get; set; } = "";
    }

    public class ParsedReceiptDto
    {
        public string ProductType { get; set; } = "";
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public DateTime Date { get; set; }
        public decimal Confidence { get; set; }
        public string Brand { get; set; } = "";
        public string Size { get; set; } = "";
        public string Color { get; set; } = "";
        public string Notes { get; set; } = "";
    }
}
