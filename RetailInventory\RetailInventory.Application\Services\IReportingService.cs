using RetailInventory.Application.DTOs;

namespace RetailInventory.Application.Services;

public interface IReportingService
{
    Task<DashboardMetricsDto> GetDashboardMetricsAsync();
    Task<List<SalesTrendDto>> GetSalesTrendAsync(DateTime startDate, DateTime endDate, string groupBy = "Day");
    Task<List<ProductPerformanceDto>> GetTopPerformingProductsAsync(int count = 10, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<CategoryPerformanceDto>> GetCategoryPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<InventoryAnalyticsDto>> GetInventoryAnalyticsAsync();
    Task<List<SalesSourceAnalyticsDto>> GetSalesSourceAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<ProfitabilityAnalysisDto>> GetProfitabilityAnalysisAsync(DateTime startDate, DateTime endDate, string groupBy = "Month");
    Task<List<LowStockAlertDto>> GetLowStockAlertsAsync();
    Task<List<RevenueBreakdownDto>> GetRevenueBreakdownAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<SalesVelocityDto>> GetSalesVelocityAsync();
    Task<FinancialSummaryDto> GetFinancialSummaryAsync(DateTime startDate, DateTime endDate);
    Task<ChartDataDto> GetSalesChartDataAsync(DateTime startDate, DateTime endDate, string groupBy = "Day");
    Task<ChartDataDto> GetProfitChartDataAsync(DateTime startDate, DateTime endDate, string groupBy = "Day");
    Task<ChartDataDto> GetCategoryRevenueChartDataAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<ChartDataDto> GetInventoryValueChartDataAsync();
}
