@page "/sales/voice"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>Voice Entry - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h3" GutterBottom="true">🎤 Voice Ledger</MudText>
    <MudText Typo="Typo.body1" Class="mb-4">Speak your sales naturally - our AI will handle the rest!</MudText>

    <MudGrid>
        <MudItem xs="12" md="8">
            <MudCard Elevation="3" Class="pa-6">
                <MudCardContent Class="pa-0">
                    <!-- Voice Recording Section -->
                    <div class="text-center mb-6">
                        <MudButton Variant="Variant.Filled" 
                                   Color="@(_isRecording ? Color.Error : Color.Primary)" 
                                   Size="Size.Large"
                                   StartIcon="@(_isRecording ? Icons.Material.Filled.Stop : Icons.Material.Filled.Mic)"
                                   OnClick="ToggleRecording"
                                   Class="pa-4"
                                   Style="border-radius: 50%; width: 120px; height: 120px;">
                            @(_isRecording ? "Stop" : "Start")
                        </MudButton>
                        
                        @if (_isRecording)
                        {
                            <MudText Typo="Typo.h6" Color="Color.Error" Class="mt-3">🔴 Recording...</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Default">Speak clearly: "Sold 3 Nike shoes for 15000 rupees"</MudText>
                        }
                        else
                        {
                            <MudText Typo="Typo.h6" Class="mt-3">Ready to Record</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Default">Click the microphone to start</MudText>
                        }
                    </div>

                    <!-- Text Input Alternative -->
                    <MudDivider Class="my-4" />
                    <MudText Typo="Typo.h6" Class="mb-3">Or Type Your Command</MudText>
                    <MudTextField @bind-Value="@_textCommand"
                                  Label="Voice Command" 
                                  Placeholder="e.g., Add 5 pairs Nike sneakers at 2500 rupees each"
                                  Variant="Variant.Outlined"
                                  Lines="3"
                                  FullWidth="true"
                                  Class="mb-3" />
                    
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Secondary" 
                               StartIcon="@Icons.Material.Filled.Send"
                               OnClick="ProcessTextCommand"
                               Disabled="@(string.IsNullOrWhiteSpace(_textCommand) || _isProcessing)">
                        @if (_isProcessing)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                            <span>Processing...</span>
                        }
                        else
                        {
                            <span>Process Command</span>
                        }
                    </MudButton>
                </MudCardContent>
            </MudCard>

            <!-- Processing Results -->
            @if (_lastResult != null)
            {
                <MudCard Elevation="2" Class="mt-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                @if (_lastResult.IsSuccess)
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Class="mr-2" />
                                    <span>Command Processed Successfully</span>
                                }
                                else
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.Error" Color="Color.Error" Class="mr-2" />
                                    <span>Processing Failed</span>
                                }
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_lastResult.IsSuccess && _lastResult.ParsedSale != null)
                        {
                            <MudText Typo="Typo.body2" Class="mb-2">
                                <strong>Transcribed:</strong> "@_lastResult.TranscribedText"
                            </MudText>
                            <MudText Typo="Typo.body2" Class="mb-3">
                                <strong>Confidence:</strong> @(_lastResult.Confidence.ToString("P0"))
                            </MudText>
                            
                            <MudDivider Class="my-3" />
                            
                            <MudText Typo="Typo.h6" Class="mb-2">Parsed Sale Information:</MudText>
                            <MudSimpleTable Dense="true">
                                <tbody>
                                    <tr>
                                        <td><strong>Product:</strong></td>
                                        <td>@_lastResult.ParsedSale.ProductName</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Quantity:</strong></td>
                                        <td>@_lastResult.ParsedSale.Quantity @_lastResult.ParsedSale.Unit</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Price per Unit:</strong></td>
                                        <td>₹@_lastResult.ParsedSale.PricePerUnit.ToString("N0")</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Amount:</strong></td>
                                        <td>₹@_lastResult.ParsedSale.TotalAmount.ToString("N0")</td>
                                    </tr>
                                    @if (!string.IsNullOrEmpty(_lastResult.ParsedSale.Size))
                                    {
                                        <tr>
                                            <td><strong>Size:</strong></td>
                                            <td>@_lastResult.ParsedSale.Size</td>
                                        </tr>
                                    }
                                    @if (!string.IsNullOrEmpty(_lastResult.ParsedSale.Color))
                                    {
                                        <tr>
                                            <td><strong>Color:</strong></td>
                                            <td>@_lastResult.ParsedSale.Color</td>
                                        </tr>
                                    }
                                </tbody>
                            </MudSimpleTable>
                            
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Success" 
                                       StartIcon="@Icons.Material.Filled.Save"
                                       OnClick="CreateSaleFromVoice"
                                       Class="mt-3"
                                       Disabled="@_isCreatingSale">
                                @if (_isCreatingSale)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                    <span>Creating Sale...</span>
                                }
                                else
                                {
                                    <span>Create Sale Entry</span>
                                }
                            </MudButton>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Error">
                                @_lastResult.ErrorMessage
                            </MudAlert>
                        }
                    </MudCardContent>
                </MudCard>
            }
        </MudItem>

        <MudItem xs="12" md="4">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">💡 Voice Command Examples</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList Dense="true">
                        <MudListItem>
                            <MudText Typo="Typo.body2">
                                <strong>"Add 5 pairs Nike sneakers at 2500 rupees each"</strong>
                            </MudText>
                        </MudListItem>
                        <MudDivider />
                        <MudListItem>
                            <MudText Typo="Typo.body2">
                                <strong>"Sell 3 Adidas shoes for 18000 rupees"</strong>
                            </MudText>
                        </MudListItem>
                        <MudDivider />
                        <MudListItem>
                            <MudText Typo="Typo.body2">
                                <strong>"Record sale 2 Nike Air Max size 10 black color"</strong>
                            </MudText>
                        </MudListItem>
                        <MudDivider />
                        <MudListItem>
                            <MudText Typo="Typo.body2">
                                <strong>"Add 4 pairs women heels red color at 2800 per pair"</strong>
                            </MudText>
                        </MudListItem>
                    </MudList>
                </MudCardContent>
            </MudCard>

            <MudCard Elevation="2" Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📊 Voice Stats</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudText Typo="Typo.body2" Class="mb-2">
                        <strong>Commands Today:</strong> 23
                    </MudText>
                    <MudText Typo="Typo.body2" Class="mb-2">
                        <strong>Success Rate:</strong> 94%
                    </MudText>
                    <MudText Typo="Typo.body2" Class="mb-2">
                        <strong>Time Saved:</strong> 2.5 hours
                    </MudText>
                    <MudText Typo="Typo.body2">
                        <strong>Sales via Voice:</strong> ₹45,600
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private bool _isRecording = false;
    private bool _isProcessing = false;
    private bool _isCreatingSale = false;
    private string _textCommand = "";
    private VoiceCommandResultDto? _lastResult;

    private async Task ToggleRecording()
    {
        if (_isRecording)
        {
            await StopRecording();
        }
        else
        {
            await StartRecording();
        }
    }

    private async Task StartRecording()
    {
        try
        {
            _isRecording = true;
            StateHasChanged();

            // Simulate recording for demo
            await Task.Delay(3000);

            // Auto-stop after 3 seconds for demo
            if (_isRecording)
            {
                await StopRecording();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Recording failed: {ex.Message}", Severity.Error);
            _isRecording = false;
        }
    }

    private async Task StopRecording()
    {
        try
        {
            _isRecording = false;
            _isProcessing = true;
            StateHasChanged();

            // Simulate processing recorded audio
            var sampleCommands = new[]
            {
                "Add 5 pairs Nike sneakers size 42 at 2500 rupees each",
                "Sell 3 Adidas Ultraboost for 18600 rupees",
                "Record sale 2 formal Oxford shoes brown color size 9"
            };

            var randomCommand = sampleCommands[new Random().Next(sampleCommands.Length)];
            _textCommand = randomCommand;

            await ProcessVoiceCommand(randomCommand);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Processing failed: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isProcessing = false;
            StateHasChanged();
        }
    }

    private async Task ProcessTextCommand()
    {
        if (string.IsNullOrWhiteSpace(_textCommand))
            return;

        await ProcessVoiceCommand(_textCommand);
    }

    private async Task ProcessVoiceCommand(string command)
    {
        try
        {
            _isProcessing = true;
            StateHasChanged();

            // Call the AI service
            var response = await Http.PostAsJsonAsync("/api/ai/voice/text", command);

            if (response.IsSuccessStatusCode)
            {
                _lastResult = await response.Content.ReadFromJsonAsync<VoiceCommandResultDto>();

                if (_lastResult?.IsSuccess == true)
                {
                    Snackbar.Add("Voice command processed successfully!", Severity.Success);
                }
                else
                {
                    Snackbar.Add(_lastResult?.ErrorMessage ?? "Failed to process command", Severity.Warning);
                }
            }
            else
            {
                Snackbar.Add("Failed to connect to AI service", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isProcessing = false;
            StateHasChanged();
        }
    }

    private async Task CreateSaleFromVoice()
    {
        if (_lastResult?.ParsedSale == null)
            return;

        try
        {
            _isCreatingSale = true;
            StateHasChanged();

            var voiceCommand = new VoiceCommandDto
            {
                TranscribedText = _lastResult.TranscribedText
            };

            var response = await Http.PostAsJsonAsync("/api/ai/voice/create-sale", voiceCommand);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                Snackbar.Add("Sale created successfully from voice command!", Severity.Success);

                // Reset form
                _textCommand = "";
                _lastResult = null;
            }
            else
            {
                Snackbar.Add("Failed to create sale", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error creating sale: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isCreatingSale = false;
            StateHasChanged();
        }
    }

    // DTO classes for API communication
    public class VoiceCommandDto
    {
        public string AudioBase64 { get; set; } = "";
        public string TranscribedText { get; set; } = "";
        public string Language { get; set; } = "en-US";
        public DateTime RecordedAt { get; set; } = DateTime.UtcNow;
    }

    public class VoiceCommandResultDto
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = "";
        public string TranscribedText { get; set; } = "";
        public VoiceParsedSaleDto? ParsedSale { get; set; }
        public decimal Confidence { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
        public string CommandId { get; set; } = "";
    }

    public class VoiceParsedSaleDto
    {
        public string ProductName { get; set; } = "";
        public decimal Quantity { get; set; }
        public string Unit { get; set; } = "";
        public decimal PricePerUnit { get; set; }
        public string Currency { get; set; } = "INR";
        public string Size { get; set; } = "";
        public string Color { get; set; } = "";
        public string Brand { get; set; } = "";
        public string Notes { get; set; } = "";
        public decimal TotalAmount { get; set; }
    }
}
