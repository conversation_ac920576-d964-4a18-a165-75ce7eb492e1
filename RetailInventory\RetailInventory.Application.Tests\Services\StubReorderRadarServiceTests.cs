using FluentAssertions;
using Moq;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Services;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Enums;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Tests.Services;

public class StubReorderRadarServiceTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IRepository<Product>> _mockProductRepository;
    private readonly StubReorderRadarService _service;

    public StubReorderRadarServiceTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockProductRepository = new Mock<IRepository<Product>>();
        _mockUnitOfWork.Setup(u => u.Products).Returns(_mockProductRepository.Object);
        _service = new StubReorderRadarService(_mockUnitOfWork.Object);
    }

    [Fact]
    public async Task GenerateReorderSuggestionsAsync_ShouldReturnSuggestions()
    {
        // Arrange
        var products = new List<Product>
        {
            new() 
            { 
                Id = 1, 
                Name = "Low Stock Product", 
                CurrentQuantity = 5, 
                ReorderThreshold = 10, 
                UnitOfMeasure = UnitOfMeasure.Pairs,
                IsActive = true 
            },
            new() 
            { 
                Id = 2, 
                Name = "Normal Stock Product", 
                CurrentQuantity = 20, 
                ReorderThreshold = 10, 
                UnitOfMeasure = UnitOfMeasure.Pairs,
                IsActive = true 
            }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products);

        // Act
        var result = await _service.GenerateReorderSuggestionsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Suggestions.Should().NotBeEmpty();
        result.TotalCriticalItems.Should().BeGreaterOrEqualTo(0);
        result.TotalHighPriorityItems.Should().BeGreaterOrEqualTo(0);
        result.TotalEstimatedCost.Should().BeGreaterOrEqualTo(0);
        result.Summary.Should().NotBeEmpty();
        result.GeneratedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task GenerateReorderSuggestionsAsync_WithNoProducts_ShouldReturnEmptyResult()
    {
        // Arrange
        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(new List<Product>());

        // Act
        var result = await _service.GenerateReorderSuggestionsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Suggestions.Should().BeEmpty();
        result.TotalCriticalItems.Should().Be(0);
        result.TotalHighPriorityItems.Should().Be(0);
        result.TotalEstimatedCost.Should().Be(0);
    }

    [Fact]
    public async Task CalculateSalesVelocityAsync_ShouldReturnVelocityData()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Id = 1, Name = "Product 1", IsActive = true },
            new() { Id = 2, Name = "Product 2", IsActive = true }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products);

        // Act
        var result = await _service.CalculateSalesVelocityAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().AllSatisfy(v =>
        {
            v.ProductId.Should().BeGreaterThan(0);
            v.ProductName.Should().NotBeEmpty();
            v.Last7DaysAverage.Should().BeGreaterOrEqualTo(0);
            v.Last30DaysAverage.Should().BeGreaterOrEqualTo(0);
            v.TrendDirection.Should().NotBeEmpty();
            v.TotalSalesLast30Days.Should().BeGreaterOrEqualTo(0);
        });
    }

    [Fact]
    public async Task GetProductReorderSuggestionAsync_WithValidProduct_ShouldReturnSuggestion()
    {
        // Arrange
        var productId = 1;
        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            CurrentQuantity = 5,
            ReorderThreshold = 10,
            UnitOfMeasure = UnitOfMeasure.Pairs,
            IsActive = true
        };

        _mockProductRepository.Setup(r => r.GetByIdAsync(productId))
            .ReturnsAsync(product);

        // Act
        var result = await _service.GetProductReorderSuggestionAsync(productId);

        // Assert
        result.Should().NotBeNull();
        result.ProductId.Should().Be(productId);
        result.ProductName.Should().Be("Test Product");
        result.CurrentStock.Should().Be(5);
        result.ReorderThreshold.Should().Be(10);
        result.UnitOfMeasure.Should().Be("Pairs");
    }

    [Fact]
    public async Task GetProductReorderSuggestionAsync_WithInvalidProduct_ShouldThrowException()
    {
        // Arrange
        var productId = 999;
        _mockProductRepository.Setup(r => r.GetByIdAsync(productId))
            .ReturnsAsync((Product?)null);

        // Act & Assert
        var action = async () => await _service.GetProductReorderSuggestionAsync(productId);
        await action.Should().ThrowAsync<ReorderRadarException>()
            .WithMessage("Product with ID 999 not found");
    }

    [Fact]
    public async Task CalculateOptimalReorderPointAsync_WithValidProduct_ShouldReturnReorderPoint()
    {
        // Arrange
        var productId = 1;
        var product = new Product
        {
            Id = productId,
            Name = "Test Product",
            ReorderThreshold = 10,
            IsActive = true
        };

        _mockProductRepository.Setup(r => r.GetByIdAsync(productId))
            .ReturnsAsync(product);

        // Act
        var result = await _service.CalculateOptimalReorderPointAsync(productId, 7);

        // Assert
        result.Should().BeGreaterOrEqualTo(0);
        result.Should().BeGreaterOrEqualTo(product.ReorderThreshold); // Should be at least the current threshold
    }

    [Fact]
    public async Task CalculateOptimalReorderPointAsync_WithInvalidProduct_ShouldReturnZero()
    {
        // Arrange
        var productId = 999;
        _mockProductRepository.Setup(r => r.GetByIdAsync(productId))
            .ReturnsAsync((Product?)null);

        // Act
        var result = await _service.CalculateOptimalReorderPointAsync(productId);

        // Assert
        result.Should().Be(0);
    }

    [Fact]
    public async Task GetCriticalStockAlertsAsync_ShouldReturnCriticalItems()
    {
        // Arrange
        var products = new List<Product>
        {
            new() 
            { 
                Id = 1, 
                Name = "Critical Stock Product", 
                CurrentQuantity = 0, 
                ReorderThreshold = 10, 
                UnitOfMeasure = UnitOfMeasure.Pairs,
                IsActive = true 
            },
            new() 
            { 
                Id = 2, 
                Name = "Low Stock Product", 
                CurrentQuantity = 5, 
                ReorderThreshold = 10, 
                UnitOfMeasure = UnitOfMeasure.Pairs,
                IsActive = true 
            }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products);

        // Act
        var result = await _service.GetCriticalStockAlertsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().NotBeEmpty();
        result.Should().AllSatisfy(suggestion =>
        {
            suggestion.Priority.Should().Be(ReorderPriority.Critical);
            suggestion.CurrentStock.Should().BeLessOrEqualTo(suggestion.ReorderThreshold);
        });
    }

    [Fact]
    public async Task GenerateReorderSuggestionsAsync_ShouldHandleExceptions()
    {
        // Arrange
        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        var action = async () => await _service.GenerateReorderSuggestionsAsync();
        await action.Should().ThrowAsync<ReorderRadarException>()
            .WithMessage("Failed to generate reorder suggestions: Database error");
    }

    [Fact]
    public async Task ReorderSuggestion_ShouldHaveValidPriorityCalculation()
    {
        // Arrange
        var products = new List<Product>
        {
            new() 
            { 
                Id = 1, 
                Name = "Zero Stock Product", 
                CurrentQuantity = 0, 
                ReorderThreshold = 10, 
                UnitOfMeasure = UnitOfMeasure.Pairs,
                IsActive = true 
            }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products);

        // Act
        var result = await _service.GenerateReorderSuggestionsAsync();

        // Assert
        result.Suggestions.Should().NotBeEmpty();
        var criticalSuggestion = result.Suggestions.FirstOrDefault(s => s.CurrentStock == 0);
        criticalSuggestion.Should().NotBeNull();
        criticalSuggestion!.Priority.Should().Be(ReorderPriority.Critical);
    }

    [Fact]
    public async Task SalesVelocity_ShouldBeOrderedByLast7DaysAverage()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Id = 1, Name = "Product 1", IsActive = true },
            new() { Id = 2, Name = "Product 2", IsActive = true },
            new() { Id = 3, Name = "Product 3", IsActive = true }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products);

        // Act
        var result = await _service.CalculateSalesVelocityAsync();

        // Assert
        result.Should().NotBeEmpty();
        for (int i = 0; i < result.Count - 1; i++)
        {
            result[i].Last7DaysAverage.Should().BeGreaterOrEqualTo(result[i + 1].Last7DaysAverage);
        }
    }
}
