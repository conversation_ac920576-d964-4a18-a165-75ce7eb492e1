@page "/stock/receive"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Receive Stock - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-4">
    <div class="d-flex align-center mb-4">
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" 
                       Color="Color.Default" 
                       OnClick="GoBack" />
        <MudText Typo="Typo.h4" Class="ml-2">Receive Stock</MudText>
    </div>

    <MudCard Elevation="3">
        <MudCardContent>
            <EditForm Model="_stockDto" OnValidSubmit="HandleSubmit">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <!-- Product Selection -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-3">🎯 Product Selection</MudText>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudAutocomplete T="ProductDto"
                                         @bind-Value="_selectedProduct"
                                         SearchFunc="SearchProducts"
                                         ToStringFunc="@(p => p?.Name ?? "")"
                                         Label="Select Product"
                                         Variant="Variant.Outlined"
                                         Required="true"
                                         AdornmentIcon="@Icons.Material.Filled.Search"
                                         AdornmentColor="Color.Primary">
                            <ItemTemplate Context="product">
                                <div class="d-flex align-center">
                                    <div class="flex-grow-1">
                                        <MudText Typo="Typo.body1"><strong>@product.Name</strong></MudText>
                                        <MudText Typo="Typo.body2" Color="Color.Default">
                                            @product.Specifications | Current: @product.CurrentQuantity.ToString("N0") @GetUnitDisplay(product.UnitOfMeasure)
                                        </MudText>
                                    </div>
                                    <MudChip Size="Size.Small" Color="GetCategoryColor(product.Category)">
                                        @product.Category
                                    </MudChip>
                                </div>
                            </ItemTemplate>
                        </MudAutocomplete>
                    </MudItem>

                    @if (_selectedProduct != null)
                    {
                        <!-- Current Stock Info -->
                        <MudItem xs="12" Class="mt-4">
                            <MudAlert Severity="Severity.Info" Icon="@Icons.Material.Filled.Info">
                                <strong>Current Stock:</strong> @_selectedProduct.CurrentQuantity.ToString("N0") @GetUnitDisplay(_selectedProduct.UnitOfMeasure) | 
                                <strong>Reorder Level:</strong> @_selectedProduct.ReorderThreshold.ToString("N0") @GetUnitDisplay(_selectedProduct.UnitOfMeasure)
                                @if (_selectedProduct.IsLowStock)
                                {
                                    <br/><strong style="color: orange;">⚠️ This product is currently low on stock!</strong>
                                }
                            </MudAlert>
                        </MudItem>

                        <!-- Stock Details -->
                        <MudItem xs="12" Class="mt-4">
                            <MudText Typo="Typo.h6" Class="mb-3">📋 Stock Details</MudText>
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudNumericField @bind-Value="_stockDto.Quantity"
                                             Label="@($"Quantity ({GetUnitDisplay(_selectedProduct.UnitOfMeasure)})")"
                                             Variant="Variant.Outlined"
                                             Min="0.01m"
                                             Step="1"
                                             Required="true"
                                             For="@(() => _stockDto.Quantity)"
                                             HelperText="Enter the quantity received" />
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudNumericField @bind-Value="_stockDto.CostPerUnit"
                                             Label="@($"Cost per {GetUnitDisplay(_selectedProduct.UnitOfMeasure)} (₹)")"
                                             Variant="Variant.Outlined"
                                             Min="0.01m"
                                             Step="0.01m"
                                             Required="true"
                                             For="@(() => _stockDto.CostPerUnit)"
                                             HelperText="Enter the cost per unit" />
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudTextField @bind-Value="_stockDto.BatchNumber"
                                          Label="Batch Number"
                                          Variant="Variant.Outlined"
                                          HelperText="Optional batch identifier" />
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudDatePicker @bind-Date="_receivedDate"
                                           Label="Received Date"
                                           Variant="Variant.Outlined"
                                           Required="true"
                                           MaxDate="DateTime.Today"
                                           HelperText="Date when stock was received" />
                        </MudItem>
                        
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_stockDto.Notes"
                                          Label="Notes"
                                          Variant="Variant.Outlined"
                                          Lines="3"
                                          HelperText="Optional notes about this stock batch" />
                        </MudItem>

                        <!-- Summary -->
                        <MudItem xs="12" Class="mt-4">
                            <MudCard Elevation="1" Class="pa-4" Style="background-color: var(--mud-palette-background-grey);">
                                <MudText Typo="Typo.h6" Class="mb-2">📊 Summary</MudText>
                                <MudGrid>
                                    <MudItem xs="6" md="3">
                                        <MudText Typo="Typo.body2" Color="Color.Default">Quantity:</MudText>
                                        <MudText Typo="Typo.body1"><strong>@_stockDto.Quantity.ToString("N2") @GetUnitDisplay(_selectedProduct.UnitOfMeasure)</strong></MudText>
                                    </MudItem>
                                    <MudItem xs="6" md="3">
                                        <MudText Typo="Typo.body2" Color="Color.Default">Cost per Unit:</MudText>
                                        <MudText Typo="Typo.body1"><strong>₹@_stockDto.CostPerUnit.ToString("N2")</strong></MudText>
                                    </MudItem>
                                    <MudItem xs="6" md="3">
                                        <MudText Typo="Typo.body2" Color="Color.Default">Total Cost:</MudText>
                                        <MudText Typo="Typo.body1"><strong>₹@((_stockDto.Quantity * _stockDto.CostPerUnit).ToString("N2"))</strong></MudText>
                                    </MudItem>
                                    <MudItem xs="6" md="3">
                                        <MudText Typo="Typo.body2" Color="Color.Default">New Total Stock:</MudText>
                                        <MudText Typo="Typo.body1"><strong>@((_selectedProduct.CurrentQuantity + _stockDto.Quantity).ToString("N2")) units</strong></MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudCard>
                        </MudItem>

                        <!-- Action Buttons -->
                        <MudItem xs="12" Class="mt-6">
                            <div class="d-flex justify-space-between">
                                <MudButton Variant="Variant.Text" 
                                           Color="Color.Default"
                                           StartIcon="@Icons.Material.Filled.Cancel"
                                           OnClick="GoBack">
                                    Cancel
                                </MudButton>
                                
                                <MudButton ButtonType="ButtonType.Submit"
                                           Variant="Variant.Filled"
                                           Color="Color.Success"
                                           StartIcon="@Icons.Material.Filled.Inventory"
                                           Disabled="_isSubmitting || _selectedProduct == null">
                                    @if (_isSubmitting)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                        <span>Receiving Stock...</span>
                                    }
                                    else
                                    {
                                        <span>Receive Stock</span>
                                    }
                                </MudButton>
                            </div>
                        </MudItem>
                    }
                </MudGrid>
            </EditForm>
        </MudCardContent>
    </MudCard>

    @if (_selectedProduct == null)
    {
        <!-- Help Card -->
        <MudCard Elevation="2" Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">💡 How to Receive Stock</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudList Dense="true">
                    <MudListItem Icon="@Icons.Material.Filled.Search" IconColor="Color.Primary">
                        <MudText Typo="Typo.body2">Start by searching and selecting the product you want to add stock for</MudText>
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Numbers" IconColor="Color.Secondary">
                        <MudText Typo="Typo.body2">Enter the quantity received and the cost per unit</MudText>
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Badge" IconColor="Color.Tertiary">
                        <MudText Typo="Typo.body2">Optionally add a batch number for tracking purposes</MudText>
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Save" IconColor="Color.Success">
                        <MudText Typo="Typo.body2">Review the summary and click "Receive Stock" to update inventory</MudText>
                    </MudListItem>
                </MudList>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private List<ProductDto> _products = new();
    private ProductDto? _selectedProduct;
    private ReceiveStockDto _stockDto = new();
    private DateTime? _receivedDate = DateTime.Today;
    private bool _isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
        
        // Check if productId is provided in query string
        var uri = new Uri(Navigation.Uri);
        var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
        if (int.TryParse(query["productId"], out var productId))
        {
            _selectedProduct = _products.FirstOrDefault(p => p.Id == productId);
        }
    }

    private async Task LoadProducts()
    {
        try
        {
            var response = await Http.GetFromJsonAsync<List<ProductDto>>("api/products");
            _products = response ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading products: {ex.Message}", Severity.Error);
        }
    }

    private async Task<IEnumerable<ProductDto>> SearchProducts(string value)
    {
        if (string.IsNullOrEmpty(value))
            return _products.Take(10);

        return _products.Where(p => 
            p.Name.Contains(value, StringComparison.OrdinalIgnoreCase) ||
            p.Specifications.Contains(value, StringComparison.OrdinalIgnoreCase))
            .Take(10);
    }

    private async Task HandleSubmit()
    {
        if (_selectedProduct == null || !_receivedDate.HasValue)
            return;

        try
        {
            _isSubmitting = true;

            _stockDto.ProductId = _selectedProduct.Id;
            _stockDto.ReceivedDate = _receivedDate.Value;

            var response = await Http.PostAsJsonAsync("api/stock/receive", _stockDto);
            
            if (response.IsSuccessStatusCode)
            {
                Snackbar.Add($"Stock received successfully! Added {_stockDto.Quantity:N2} {GetUnitDisplay(_selectedProduct.UnitOfMeasure)} to {_selectedProduct.Name}", Severity.Success);
                Navigation.NavigateTo("/products");
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"Error receiving stock: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error receiving stock: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSubmitting = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/products");
    }

    private Color GetCategoryColor(ProductCategory category)
    {
        return category switch
        {
            ProductCategory.Footwear => Color.Primary,
            ProductCategory.Leather => Color.Secondary,
            ProductCategory.Clothing => Color.Tertiary,
            ProductCategory.Accessories => Color.Info,
            _ => Color.Default
        };
    }

    private string GetUnitDisplay(UnitOfMeasure unit)
    {
        return unit switch
        {
            UnitOfMeasure.Pairs => "pairs",
            UnitOfMeasure.Pieces => "pcs",
            UnitOfMeasure.Kilograms => "kg",
            UnitOfMeasure.Meters => "m",
            UnitOfMeasure.Liters => "L",
            _ => "units"
        };
    }

    public class ReceiveStockDto
    {
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal CostPerUnit { get; set; }
        public string BatchNumber { get; set; } = "";
        public DateTime ReceivedDate { get; set; } = DateTime.Today;
        public string Notes { get; set; } = "";
    }
}
