using FluentAssertions;
using RetailInventory.Domain.ValueObjects;

namespace RetailInventory.Domain.Tests.ValueObjects;

public class MoneyTests
{
    [Fact]
    public void Money_WithValidAmount_ShouldCreateInstance()
    {
        // Arrange
        var amount = 100.50m;
        var currency = "USD";

        // Act
        var money = new Money(amount, currency);

        // Assert
        money.Amount.Should().Be(amount);
        money.Currency.Should().Be(currency);
    }

    [Fact]
    public void Money_WithDefaultCurrency_ShouldUseINR()
    {
        // Arrange
        var amount = 100m;

        // Act
        var money = new Money(amount);

        // Assert
        money.Amount.Should().Be(amount);
        money.Currency.Should().Be("INR");
    }

    [Fact]
    public void Money_WithNegativeAmount_ShouldThrowArgumentException()
    {
        // Arrange
        var amount = -100m;

        // Act & Assert
        var action = () => new Money(amount);
        action.Should().Throw<ArgumentException>()
            .WithMessage("Amount cannot be negative*");
    }

    [Fact]
    public void Money_WithNullCurrency_ShouldThrowArgumentNullException()
    {
        // Arrange
        var amount = 100m;
        string currency = null!;

        // Act & Assert
        var action = () => new Money(amount, currency);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Money_Addition_WithSameCurrency_ShouldReturnSum()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(50m, "USD");

        // Act
        var result = money1 + money2;

        // Assert
        result.Amount.Should().Be(150m);
        result.Currency.Should().Be("USD");
    }

    [Fact]
    public void Money_Addition_WithDifferentCurrency_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(50m, "EUR");

        // Act & Assert
        var action = () => money1 + money2;
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot perform operations on different currencies");
    }

    [Fact]
    public void Money_Subtraction_WithSameCurrency_ShouldReturnDifference()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(30m, "USD");

        // Act
        var result = money1 - money2;

        // Assert
        result.Amount.Should().Be(70m);
        result.Currency.Should().Be("USD");
    }

    [Fact]
    public void Money_Subtraction_ResultingInNegative_ShouldThrowArgumentException()
    {
        // Arrange
        var money1 = new Money(50m, "USD");
        var money2 = new Money(100m, "USD");

        // Act & Assert
        var action = () => money1 - money2;
        action.Should().Throw<ArgumentException>()
            .WithMessage("Amount cannot be negative*");
    }

    [Fact]
    public void Money_Multiplication_ShouldReturnScaledAmount()
    {
        // Arrange
        var money = new Money(100m, "USD");
        var multiplier = 2.5m;

        // Act
        var result = money * multiplier;

        // Assert
        result.Amount.Should().Be(250m);
        result.Currency.Should().Be("USD");
    }

    [Fact]
    public void Money_Multiplication_WithNegativeMultiplier_ShouldThrowArgumentException()
    {
        // Arrange
        var money = new Money(100m, "USD");
        var multiplier = -2m;

        // Act & Assert
        var action = () => money * multiplier;
        action.Should().Throw<ArgumentException>()
            .WithMessage("Amount cannot be negative*");
    }

    [Fact]
    public void Money_Division_ShouldReturnScaledAmount()
    {
        // Arrange
        var money = new Money(100m, "USD");
        var divisor = 4m;

        // Act
        var result = money / divisor;

        // Assert
        result.Amount.Should().Be(25m);
        result.Currency.Should().Be("USD");
    }

    [Fact]
    public void Money_Division_ByZero_ShouldThrowDivideByZeroException()
    {
        // Arrange
        var money = new Money(100m, "USD");
        var divisor = 0m;

        // Act & Assert
        var action = () => money / divisor;
        action.Should().Throw<DivideByZeroException>();
    }

    [Fact]
    public void Money_Equality_WithSameAmountAndCurrency_ShouldBeEqual()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(100m, "USD");

        // Act & Assert
        money1.Should().Be(money2);
        (money1 == money2).Should().BeTrue();
        (money1 != money2).Should().BeFalse();
    }

    [Fact]
    public void Money_Equality_WithDifferentAmount_ShouldNotBeEqual()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(200m, "USD");

        // Act & Assert
        money1.Should().NotBe(money2);
        (money1 == money2).Should().BeFalse();
        (money1 != money2).Should().BeTrue();
    }

    [Fact]
    public void Money_Equality_WithDifferentCurrency_ShouldNotBeEqual()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(100m, "EUR");

        // Act & Assert
        money1.Should().NotBe(money2);
    }

    [Fact]
    public void Money_ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var money = new Money(100.50m, "USD");

        // Act
        var result = money.ToString();

        // Assert
        result.Should().Be("100.50 USD");
    }

    [Fact]
    public void Money_GetHashCode_WithSameValues_ShouldReturnSameHashCode()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(100m, "USD");

        // Act & Assert
        money1.GetHashCode().Should().Be(money2.GetHashCode());
    }

    [Fact]
    public void Money_IsZero_WithZeroAmount_ShouldReturnTrue()
    {
        // Arrange
        var money = new Money(0m, "USD");

        // Act & Assert
        money.IsZero.Should().BeTrue();
    }

    [Fact]
    public void Money_IsZero_WithNonZeroAmount_ShouldReturnFalse()
    {
        // Arrange
        var money = new Money(100m, "USD");

        // Act & Assert
        money.IsZero.Should().BeFalse();
    }
}
