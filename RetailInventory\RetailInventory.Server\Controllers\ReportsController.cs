using Microsoft.AspNetCore.Mvc;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Services;

namespace RetailInventory.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ReportsController : ControllerBase
{
    private readonly IReportingService _reportingService;

    public ReportsController(IReportingService reportingService)
    {
        _reportingService = reportingService;
    }

    [HttpGet("dashboard")]
    public async Task<ActionResult<DashboardMetricsDto>> GetDashboardMetrics()
    {
        try
        {
            var metrics = await _reportingService.GetDashboardMetricsAsync();
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving dashboard metrics: {ex.Message}");
        }
    }

    [HttpGet("sales-trend")]
    public async Task<ActionResult<List<SalesTrendDto>>> GetSalesTrend(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string groupBy = "Day")
    {
        try
        {
            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;
            
            var trends = await _reportingService.GetSalesTrendAsync(start, end, groupBy);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving sales trends: {ex.Message}");
        }
    }

    [HttpGet("top-products")]
    public async Task<ActionResult<List<ProductPerformanceDto>>> GetTopPerformingProducts(
        [FromQuery] int count = 10,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var products = await _reportingService.GetTopPerformingProductsAsync(count, startDate, endDate);
            return Ok(products);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving top products: {ex.Message}");
        }
    }

    [HttpGet("category-performance")]
    public async Task<ActionResult<List<CategoryPerformanceDto>>> GetCategoryPerformance(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var categories = await _reportingService.GetCategoryPerformanceAsync(startDate, endDate);
            return Ok(categories);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving category performance: {ex.Message}");
        }
    }

    [HttpGet("inventory-analytics")]
    public async Task<ActionResult<List<InventoryAnalyticsDto>>> GetInventoryAnalytics()
    {
        try
        {
            var analytics = await _reportingService.GetInventoryAnalyticsAsync();
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving inventory analytics: {ex.Message}");
        }
    }

    [HttpGet("sales-source-analytics")]
    public async Task<ActionResult<List<SalesSourceAnalyticsDto>>> GetSalesSourceAnalytics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var analytics = await _reportingService.GetSalesSourceAnalyticsAsync(startDate, endDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving sales source analytics: {ex.Message}");
        }
    }

    [HttpGet("profitability-analysis")]
    public async Task<ActionResult<List<ProfitabilityAnalysisDto>>> GetProfitabilityAnalysis(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string groupBy = "Month")
    {
        try
        {
            var start = startDate ?? DateTime.Today.AddMonths(-12);
            var end = endDate ?? DateTime.Today;
            
            var analysis = await _reportingService.GetProfitabilityAnalysisAsync(start, end, groupBy);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving profitability analysis: {ex.Message}");
        }
    }

    [HttpGet("low-stock-alerts")]
    public async Task<ActionResult<List<LowStockAlertDto>>> GetLowStockAlerts()
    {
        try
        {
            var alerts = await _reportingService.GetLowStockAlertsAsync();
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving low stock alerts: {ex.Message}");
        }
    }

    [HttpGet("revenue-breakdown")]
    public async Task<ActionResult<List<RevenueBreakdownDto>>> GetRevenueBreakdown(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var breakdown = await _reportingService.GetRevenueBreakdownAsync(startDate, endDate);
            return Ok(breakdown);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving revenue breakdown: {ex.Message}");
        }
    }

    [HttpGet("sales-velocity")]
    public async Task<ActionResult<List<SalesVelocityDto>>> GetSalesVelocity()
    {
        try
        {
            var velocity = await _reportingService.GetSalesVelocityAsync();
            return Ok(velocity);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving sales velocity: {ex.Message}");
        }
    }

    [HttpGet("financial-summary")]
    public async Task<ActionResult<FinancialSummaryDto>> GetFinancialSummary(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var start = startDate ?? DateTime.Today.AddMonths(-1);
            var end = endDate ?? DateTime.Today;
            
            var summary = await _reportingService.GetFinancialSummaryAsync(start, end);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving financial summary: {ex.Message}");
        }
    }

    [HttpGet("charts/sales")]
    public async Task<ActionResult<ChartDataDto>> GetSalesChartData(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string groupBy = "Day")
    {
        try
        {
            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;
            
            var chartData = await _reportingService.GetSalesChartDataAsync(start, end, groupBy);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving sales chart data: {ex.Message}");
        }
    }

    [HttpGet("charts/profit")]
    public async Task<ActionResult<ChartDataDto>> GetProfitChartData(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string groupBy = "Day")
    {
        try
        {
            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;
            
            var chartData = await _reportingService.GetProfitChartDataAsync(start, end, groupBy);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving profit chart data: {ex.Message}");
        }
    }

    [HttpGet("charts/category-revenue")]
    public async Task<ActionResult<ChartDataDto>> GetCategoryRevenueChartData(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var chartData = await _reportingService.GetCategoryRevenueChartDataAsync(startDate, endDate);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving category revenue chart data: {ex.Message}");
        }
    }

    [HttpGet("charts/inventory-value")]
    public async Task<ActionResult<ChartDataDto>> GetInventoryValueChartData()
    {
        try
        {
            var chartData = await _reportingService.GetInventoryValueChartDataAsync();
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving inventory value chart data: {ex.Message}");
        }
    }
}
