using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Enums;
using RetailInventory.Infrastructure.Data;
using System.Net;
using System.Net.Http.Json;

namespace RetailInventory.Server.Tests.Integration;

public class ProductsIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ProductsIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<RetailInventoryDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<RetailInventoryDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb");
                });
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetProducts_ShouldReturnEmptyList_WhenNoProductsExist()
    {
        // Act
        var response = await _client.GetAsync("/api/products");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var products = await response.Content.ReadFromJsonAsync<List<ProductDto>>();
        products.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public async Task CreateProduct_ShouldCreateAndReturnProduct()
    {
        // Arrange
        var createDto = new CreateProductDto
        {
            Name = "Integration Test Product",
            Category = ProductCategory.Footwear,
            Specifications = "Size 8-12",
            Grade = "Premium",
            Color = "Black",
            ReorderThreshold = 10,
            UnitOfMeasure = UnitOfMeasure.Pairs
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/products", createDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        var createdProduct = await response.Content.ReadFromJsonAsync<ProductDto>();
        createdProduct.Should().NotBeNull();
        createdProduct!.Name.Should().Be(createDto.Name);
        createdProduct.Category.Should().Be(createDto.Category);
    }

    [Fact]
    public async Task GetProduct_WithValidId_ShouldReturnProduct()
    {
        // Arrange - Create a product first
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<RetailInventoryDbContext>();
        
        var product = new Product
        {
            Name = "Test Product for Get",
            Category = ProductCategory.Footwear,
            Specifications = "Test specs",
            Grade = "Standard",
            Color = "Blue",
            ReorderThreshold = 5,
            UnitOfMeasure = UnitOfMeasure.Pairs,
            IsActive = true
        };

        context.Products.Add(product);
        await context.SaveChangesAsync();

        // Act
        var response = await _client.GetAsync($"/api/products/{product.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var retrievedProduct = await response.Content.ReadFromJsonAsync<ProductDto>();
        retrievedProduct.Should().NotBeNull();
        retrievedProduct!.Id.Should().Be(product.Id);
        retrievedProduct.Name.Should().Be(product.Name);
    }

    [Fact]
    public async Task GetProduct_WithInvalidId_ShouldReturnNotFound()
    {
        // Act
        var response = await _client.GetAsync("/api/products/999");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task UpdateProduct_WithValidData_ShouldUpdateProduct()
    {
        // Arrange - Create a product first
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<RetailInventoryDbContext>();
        
        var product = new Product
        {
            Name = "Original Name",
            Category = ProductCategory.Footwear,
            Specifications = "Original specs",
            Grade = "Standard",
            Color = "Red",
            ReorderThreshold = 5,
            UnitOfMeasure = UnitOfMeasure.Pairs,
            IsActive = true
        };

        context.Products.Add(product);
        await context.SaveChangesAsync();

        var updateDto = new UpdateProductDto
        {
            Name = "Updated Name",
            Category = ProductCategory.Leather,
            Specifications = "Updated specs",
            Grade = "Premium",
            Color = "Green",
            ReorderThreshold = 15,
            UnitOfMeasure = UnitOfMeasure.Pieces
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/products/{product.Id}", updateDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var updatedProduct = await response.Content.ReadFromJsonAsync<ProductDto>();
        updatedProduct.Should().NotBeNull();
        updatedProduct!.Name.Should().Be(updateDto.Name);
        updatedProduct.Category.Should().Be(updateDto.Category);
    }

    [Fact]
    public async Task DeleteProduct_WithValidId_ShouldSoftDeleteProduct()
    {
        // Arrange - Create a product first
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<RetailInventoryDbContext>();
        
        var product = new Product
        {
            Name = "Product to Delete",
            Category = ProductCategory.Footwear,
            IsActive = true
        };

        context.Products.Add(product);
        await context.SaveChangesAsync();

        // Act
        var response = await _client.DeleteAsync($"/api/products/{product.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Verify the product is soft deleted
        var deletedProduct = await context.Products.FindAsync(product.Id);
        deletedProduct.Should().NotBeNull();
        deletedProduct!.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task GetLowStockProducts_ShouldReturnOnlyLowStockProducts()
    {
        // Arrange - Create products with different stock levels
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<RetailInventoryDbContext>();
        
        var products = new List<Product>
        {
            new() { Name = "Low Stock 1", CurrentQuantity = 5, ReorderThreshold = 10, IsActive = true },
            new() { Name = "Low Stock 2", CurrentQuantity = 8, ReorderThreshold = 10, IsActive = true },
            new() { Name = "Normal Stock", CurrentQuantity = 15, ReorderThreshold = 10, IsActive = true }
        };

        context.Products.AddRange(products);
        await context.SaveChangesAsync();

        // Act
        var response = await _client.GetAsync("/api/stock/low-stock");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var lowStockProducts = await response.Content.ReadFromJsonAsync<List<ProductDto>>();
        lowStockProducts.Should().NotBeNull();
        lowStockProducts!.Should().HaveCount(2);
        lowStockProducts.Should().OnlyContain(p => p.CurrentQuantity <= p.ReorderThreshold);
    }

    [Fact]
    public async Task SearchProducts_WithSearchTerm_ShouldReturnMatchingProducts()
    {
        // Arrange - Create products
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<RetailInventoryDbContext>();
        
        var products = new List<Product>
        {
            new() { Name = "Nike Air Max", IsActive = true },
            new() { Name = "Nike Revolution", IsActive = true },
            new() { Name = "Adidas Ultraboost", IsActive = true }
        };

        context.Products.AddRange(products);
        await context.SaveChangesAsync();

        // Act
        var response = await _client.GetAsync("/api/products/search?searchTerm=Nike");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var searchResults = await response.Content.ReadFromJsonAsync<List<ProductDto>>();
        searchResults.Should().NotBeNull();
        searchResults!.Should().HaveCount(2);
        searchResults.Should().OnlyContain(p => p.Name.Contains("Nike"));
    }

    // Helper DTOs for testing
    public class CreateProductDto
    {
        public string Name { get; set; } = string.Empty;
        public ProductCategory Category { get; set; }
        public string Specifications { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal ReorderThreshold { get; set; }
        public UnitOfMeasure UnitOfMeasure { get; set; }
    }

    public class UpdateProductDto
    {
        public string Name { get; set; } = string.Empty;
        public ProductCategory Category { get; set; }
        public string Specifications { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal ReorderThreshold { get; set; }
        public UnitOfMeasure UnitOfMeasure { get; set; }
    }
}
