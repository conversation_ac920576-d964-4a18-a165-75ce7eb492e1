using Microsoft.EntityFrameworkCore;
using RetailInventory.Infrastructure.DbContext;
using RetailInventory.Domain.Interfaces;
using RetailInventory.Infrastructure.Repositories;
using RetailInventory.Infrastructure.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add Entity Framework
builder.Services.AddDbContext<RetailInventoryDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add repositories
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

// Add MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(RetailInventory.Application.Products.Queries.GetAllProductsQuery).Assembly));

// Add AI Services
builder.Services.AddScoped<RetailInventory.Application.Services.IReceiptParserService, RetailInventory.Application.Services.StubReceiptParserService>();
builder.Services.AddScoped<RetailInventory.Application.Services.IVoiceLedgerService, RetailInventory.Application.Services.StubVoiceLedgerService>();
builder.Services.AddScoped<RetailInventory.Application.Services.IReorderRadarService, RetailInventory.Application.Services.StubReorderRadarService>();

// Add Reporting Services
builder.Services.AddScoped<RetailInventory.Application.Services.IReportingService, RetailInventory.Application.Services.StubReportingService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.MapControllers();

var summaries = new[]
{
    "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
};

app.MapGet("/weatherforecast", () =>
{
    var forecast = Enumerable.Range(1, 5).Select(index =>
        new WeatherForecast
        (
            DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            Random.Shared.Next(-20, 55),
            summaries[Random.Shared.Next(summaries.Length)]
        ))
        .ToArray();
    return forecast;
})
.WithName("GetWeatherForecast");

app.Run();

record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}
