using FluentAssertions;
using RetailInventory.Domain.Entities;

namespace RetailInventory.Domain.Tests.Entities;

public class SaleTests
{
    [Fact]
    public void Sale_ShouldInitializeWithDefaultValues()
    {
        // Arrange & Act
        var sale = new Sale();

        // Assert
        sale.Id.Should().Be(0);
        sale.SaleDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        sale.TotalAmount.Should().Be(0);
        sale.TotalCost.Should().Be(0);
        sale.TotalProfit.Should().Be(0);
        sale.CustomerId.Should().BeNull();
        sale.Notes.Should().BeEmpty();
        sale.Source.Should().Be("Manual");
        sale.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        sale.SaleLines.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void CalculateTotals_WithNoSaleLines_ShouldSetTotalsToZero()
    {
        // Arrange
        var sale = new Sale();

        // Act
        sale.CalculateTotals();

        // Assert
        sale.TotalAmount.Should().Be(0);
        sale.TotalCost.Should().Be(0);
        sale.TotalProfit.Should().Be(0);
    }

    [Fact]
    public void CalculateTotals_WithSaleLines_ShouldCalculateCorrectTotals()
    {
        // Arrange
        var sale = new Sale();
        sale.SaleLines.Add(new SaleLine
        {
            Quantity = 2m,
            SalePricePerUnit = 100m,
            CostPerUnit = 60m,
            LineTotal = 200m
        });
        sale.SaleLines.Add(new SaleLine
        {
            Quantity = 1m,
            SalePricePerUnit = 150m,
            CostPerUnit = 90m,
            LineTotal = 150m
        });

        // Expected values:
        // TotalAmount = 200 + 150 = 350
        // TotalCost = (2 * 60) + (1 * 90) = 120 + 90 = 210
        // TotalProfit = 350 - 210 = 140

        // Act
        sale.CalculateTotals();

        // Assert
        sale.TotalAmount.Should().Be(350m);
        sale.TotalCost.Should().Be(210m);
        sale.TotalProfit.Should().Be(140m);
    }

    [Fact]
    public void GetProfitMargin_WithZeroTotalAmount_ShouldReturnZero()
    {
        // Arrange
        var sale = new Sale
        {
            TotalAmount = 0m,
            TotalProfit = 0m
        };

        // Act
        var profitMargin = sale.GetProfitMargin();

        // Assert
        profitMargin.Should().Be(0m);
    }

    [Fact]
    public void GetProfitMargin_WithValidTotals_ShouldCalculateCorrectPercentage()
    {
        // Arrange
        var sale = new Sale
        {
            TotalAmount = 1000m,
            TotalProfit = 250m
        };

        // Expected: (250 / 1000) * 100 = 25%

        // Act
        var profitMargin = sale.GetProfitMargin();

        // Assert
        profitMargin.Should().Be(25m);
    }

    [Fact]
    public void GetTotalItemsSold_WithNoSaleLines_ShouldReturnZero()
    {
        // Arrange
        var sale = new Sale();

        // Act
        var totalItems = sale.GetTotalItemsSold();

        // Assert
        totalItems.Should().Be(0);
    }

    [Fact]
    public void GetTotalItemsSold_WithSaleLines_ShouldSumQuantities()
    {
        // Arrange
        var sale = new Sale();
        sale.SaleLines.Add(new SaleLine { Quantity = 2m });
        sale.SaleLines.Add(new SaleLine { Quantity = 3.5m });
        sale.SaleLines.Add(new SaleLine { Quantity = 1m });

        // Expected: 2 + 3.5 + 1 = 6.5

        // Act
        var totalItems = sale.GetTotalItemsSold();

        // Assert
        totalItems.Should().Be(6.5m);
    }

    [Fact]
    public void AddSaleLine_ShouldAddLineToCollection()
    {
        // Arrange
        var sale = new Sale();
        var saleLine = new SaleLine
        {
            ProductId = 1,
            Quantity = 2m,
            SalePricePerUnit = 100m
        };

        // Act
        sale.AddSaleLine(saleLine);

        // Assert
        sale.SaleLines.Should().HaveCount(1);
        sale.SaleLines.Should().Contain(saleLine);
    }

    [Fact]
    public void AddSaleLine_WithNullSaleLine_ShouldThrowArgumentNullException()
    {
        // Arrange
        var sale = new Sale();

        // Act & Assert
        var action = () => sale.AddSaleLine(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void RemoveSaleLine_WithExistingLine_ShouldRemoveFromCollection()
    {
        // Arrange
        var sale = new Sale();
        var saleLine = new SaleLine { ProductId = 1 };
        sale.SaleLines.Add(saleLine);

        // Act
        var result = sale.RemoveSaleLine(saleLine);

        // Assert
        result.Should().BeTrue();
        sale.SaleLines.Should().BeEmpty();
    }

    [Fact]
    public void RemoveSaleLine_WithNonExistingLine_ShouldReturnFalse()
    {
        // Arrange
        var sale = new Sale();
        var saleLine = new SaleLine { ProductId = 1 };

        // Act
        var result = sale.RemoveSaleLine(saleLine);

        // Assert
        result.Should().BeFalse();
        sale.SaleLines.Should().BeEmpty();
    }

    [Fact]
    public void IsValidSale_WithValidSale_ShouldReturnTrue()
    {
        // Arrange
        var sale = new Sale();
        sale.SaleLines.Add(new SaleLine
        {
            ProductId = 1,
            Quantity = 1m,
            SalePricePerUnit = 100m,
            LineTotal = 100m
        });

        // Act
        var isValid = sale.IsValidSale();

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void IsValidSale_WithNoSaleLines_ShouldReturnFalse()
    {
        // Arrange
        var sale = new Sale();

        // Act
        var isValid = sale.IsValidSale();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void IsValidSale_WithInvalidSaleLines_ShouldReturnFalse()
    {
        // Arrange
        var sale = new Sale();
        sale.SaleLines.Add(new SaleLine
        {
            ProductId = 1,
            Quantity = 0m, // Invalid quantity
            SalePricePerUnit = 100m
        });

        // Act
        var isValid = sale.IsValidSale();

        // Assert
        isValid.Should().BeFalse();
    }
}
