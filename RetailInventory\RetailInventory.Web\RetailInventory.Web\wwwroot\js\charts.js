// Chart.js integration for Retail Inventory Analytics

let salesTrendChart = null;
let categoryChart = null;

// Initialize Sales Trend Chart
window.initializeSalesTrendChart = function (chartData) {
    const ctx = document.getElementById('salesTrendChart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (salesTrendChart) {
        salesTrendChart.destroy();
    }

    // Create combined sales and profit chart
    const salesData = chartData.datasets.find(d => d.label === 'Sales Revenue');
    const profitData = {
        label: 'Profit',
        data: salesData.data.map(x => x * 0.275), // 27.5% profit margin
        backgroundColor: 'rgba(56, 142, 60, 0.1)',
        borderColor: 'rgba(56, 142, 60, 1)',
        borderWidth: 2,
        fill: true,
        yAxisID: 'y1'
    };

    salesTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    ...salesData,
                    yAxisID: 'y'
                },
                profitData
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += '₹' + context.parsed.y.toLocaleString();
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Sales Revenue (₹)'
                    },
                    ticks: {
                        callback: function (value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Profit (₹)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function (value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
};

// Update Sales Trend Chart
window.updateSalesTrendChart = function (chartData) {
    if (!salesTrendChart) {
        window.initializeSalesTrendChart(chartData);
        return;
    }

    const salesData = chartData.datasets.find(d => d.label === 'Sales Revenue');
    
    salesTrendChart.data.labels = chartData.labels;
    salesTrendChart.data.datasets[0].data = salesData.data;
    salesTrendChart.data.datasets[1].data = salesData.data.map(x => x * 0.275);
    
    salesTrendChart.update();
};

// Initialize Category Revenue Pie Chart
window.initializeCategoryChart = function (chartData) {
    const ctx = document.getElementById('categoryChart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (categoryChart) {
        categoryChart.destroy();
    }

    // Generate colors for categories
    const colors = [
        'rgba(25, 118, 210, 0.8)',
        'rgba(56, 142, 60, 0.8)',
        'rgba(245, 124, 0, 0.8)',
        'rgba(156, 39, 176, 0.8)',
        'rgba(244, 67, 54, 0.8)'
    ];

    categoryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.datasets[0].data,
                backgroundColor: colors.slice(0, chartData.labels.length),
                borderColor: colors.slice(0, chartData.labels.length).map(c => c.replace('0.8', '1')),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ₹${value.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
};

// Initialize Inventory Value Bar Chart
window.initializeInventoryChart = function (chartData) {
    const ctx = document.getElementById('inventoryChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'Inventory Value',
                data: chartData.datasets[0].data,
                backgroundColor: 'rgba(245, 124, 0, 0.8)',
                borderColor: 'rgba(245, 124, 0, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return 'Value: ₹' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Value (₹)'
                    },
                    ticks: {
                        callback: function (value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Products'
                    }
                }
            }
        }
    });
};

// Initialize Profitability Analysis Chart
window.initializeProfitabilityChart = function (chartData) {
    const ctx = document.getElementById('profitabilityChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Profitability Analysis'
                },
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Amount (₹)'
                    },
                    ticks: {
                        callback: function (value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
};

// Utility function to format currency
function formatCurrency(value) {
    return '₹' + value.toLocaleString('en-IN');
}

// Utility function to generate chart colors
function generateColors(count) {
    const baseColors = [
        'rgba(25, 118, 210, 0.8)',
        'rgba(56, 142, 60, 0.8)',
        'rgba(245, 124, 0, 0.8)',
        'rgba(156, 39, 176, 0.8)',
        'rgba(244, 67, 54, 0.8)',
        'rgba(96, 125, 139, 0.8)',
        'rgba(121, 85, 72, 0.8)',
        'rgba(158, 158, 158, 0.8)'
    ];
    
    const colors = [];
    for (let i = 0; i < count; i++) {
        colors.push(baseColors[i % baseColors.length]);
    }
    return colors;
}

// Export chart as image
window.exportChart = function (chartId, filename) {
    const canvas = document.getElementById(chartId);
    if (canvas) {
        const url = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = filename || 'chart.png';
        link.href = url;
        link.click();
    }
};
