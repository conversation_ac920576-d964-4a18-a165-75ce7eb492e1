using FluentAssertions;
using Moq;
using RetailInventory.Application.Products.Queries;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Enums;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Tests.Products.Queries;

public class GetAllProductsQueryHandlerTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IRepository<Product>> _mockProductRepository;
    private readonly GetAllProductsQueryHandler _handler;

    public GetAllProductsQueryHandlerTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockProductRepository = new Mock<IRepository<Product>>();
        _mockUnitOfWork.Setup(u => u.Products).Returns(_mockProductRepository.Object);
        _handler = new GetAllProductsQueryHandler(_mockUnitOfWork.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnSuccessWithProducts_WhenProductsExist()
    {
        // Arrange
        var products = new List<Product>
        {
            new() 
            { 
                Id = 1, 
                Name = "Nike Air Max", 
                Category = ProductCategory.Footwear,
                Specifications = "Size 8-12",
                Grade = "Premium",
                Color = "Black",
                CurrentQuantity = 50,
                ReorderThreshold = 10,
                UnitOfMeasure = UnitOfMeasure.Pairs,
                IsActive = true
            },
            new() 
            { 
                Id = 2, 
                Name = "Leather Jacket", 
                Category = ProductCategory.Leather,
                Specifications = "Medium size",
                Grade = "Standard",
                Color = "Brown",
                CurrentQuantity = 25,
                ReorderThreshold = 5,
                UnitOfMeasure = UnitOfMeasure.Pieces,
                IsActive = true
            }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products);

        var query = new GetAllProductsQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().HaveCount(2);
        
        var firstProduct = result.Value.First();
        firstProduct.Id.Should().Be(1);
        firstProduct.Name.Should().Be("Nike Air Max");
        firstProduct.Category.Should().Be(ProductCategory.Footwear);
        firstProduct.CurrentQuantity.Should().Be(50);
        firstProduct.IsLowStock.Should().BeFalse();
    }

    [Fact]
    public async Task Handle_ShouldReturnSuccessWithEmptyList_WhenNoActiveProductsExist()
    {
        // Arrange
        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(new List<Product>());

        var query = new GetAllProductsQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEmpty();
    }

    [Fact]
    public async Task Handle_ShouldReturnFailure_WhenExceptionOccurs()
    {
        // Arrange
        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ThrowsAsync(new Exception("Database connection failed"));

        var query = new GetAllProductsQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Contain("Database connection failed");
    }

    [Fact]
    public async Task Handle_ShouldOnlyReturnActiveProducts()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Id = 1, Name = "Active Product", IsActive = true },
            new() { Id = 2, Name = "Inactive Product", IsActive = false }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products.Where(p => p.IsActive).ToList());

        var query = new GetAllProductsQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().HaveCount(1);
        result.Value.First().Name.Should().Be("Active Product");
    }

    [Fact]
    public async Task Handle_ShouldCalculateIsLowStockCorrectly()
    {
        // Arrange
        var products = new List<Product>
        {
            new() 
            { 
                Id = 1, 
                Name = "Low Stock Product", 
                CurrentQuantity = 5, 
                ReorderThreshold = 10, 
                IsActive = true 
            },
            new() 
            { 
                Id = 2, 
                Name = "Normal Stock Product", 
                CurrentQuantity = 20, 
                ReorderThreshold = 10, 
                IsActive = true 
            }
        };

        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(products);

        var query = new GetAllProductsQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().HaveCount(2);
        
        var lowStockProduct = result.Value.First(p => p.Name == "Low Stock Product");
        lowStockProduct.IsLowStock.Should().BeTrue();
        
        var normalStockProduct = result.Value.First(p => p.Name == "Normal Stock Product");
        normalStockProduct.IsLowStock.Should().BeFalse();
    }

    [Fact]
    public async Task Handle_ShouldVerifyRepositoryCall()
    {
        // Arrange
        _mockProductRepository.Setup(r => r.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
            .ReturnsAsync(new List<Product>());

        var query = new GetAllProductsQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockProductRepository.Verify(
            r => r.FindAsync(It.Is<System.Linq.Expressions.Expression<Func<Product, bool>>>(
                expr => expr != null)), 
            Times.Once);
    }
}
