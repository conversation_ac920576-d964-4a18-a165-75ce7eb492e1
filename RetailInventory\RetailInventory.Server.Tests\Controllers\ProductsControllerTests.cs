using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;
using MediatR;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Products.Queries;
using RetailInventory.Application.Products.Commands;
using RetailInventory.Domain.Enums;
using RetailInventory.Server.Controllers;

namespace RetailInventory.Server.Tests.Controllers;

public class ProductsControllerTests
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly ProductsController _controller;

    public ProductsControllerTests()
    {
        _mockMediator = new Mock<IMediator>();
        _controller = new ProductsController(_mockMediator.Object);
    }

    [Fact]
    public async Task GetAllProducts_ShouldReturnOkWithProducts()
    {
        // Arrange
        var products = new List<ProductDto>
        {
            new() { Id = 1, Name = "Product 1", Category = ProductCategory.Footwear },
            new() { Id = 2, Name = "Product 2", Category = ProductCategory.Leather }
        };

        var successResult = Result<List<ProductDto>>.Success(products);
        _mockMediator.Setup(m => m.Send(It.IsAny<GetAllProductsQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(successResult);

        // Act
        var result = await _controller.GetAllProducts();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(products);
    }

    [Fact]
    public async Task GetProducts_WhenServiceThrows_ShouldReturnInternalServerError()
    {
        // Arrange
        _mockProductService.Setup(s => s.GetAllProductsAsync())
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetProducts();

        // Assert
        result.Result.Should().BeOfType<ObjectResult>();
        var objectResult = result.Result as ObjectResult;
        objectResult!.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GetProduct_WithValidId_ShouldReturnOkWithProduct()
    {
        // Arrange
        var productId = 1;
        var product = new ProductDto
        {
            Id = productId,
            Name = "Test Product",
            Category = ProductCategory.Footwear
        };

        _mockProductService.Setup(s => s.GetProductByIdAsync(productId))
            .ReturnsAsync(product);

        // Act
        var result = await _controller.GetProduct(productId);

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(product);
    }

    [Fact]
    public async Task GetProduct_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var productId = 999;
        _mockProductService.Setup(s => s.GetProductByIdAsync(productId))
            .ReturnsAsync((ProductDto?)null);

        // Act
        var result = await _controller.GetProduct(productId);

        // Assert
        result.Result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task CreateProduct_WithValidData_ShouldReturnCreatedAtAction()
    {
        // Arrange
        var createDto = new CreateProductDto
        {
            Name = "New Product",
            Category = ProductCategory.Footwear,
            Specifications = "Size 8-12",
            Grade = "Premium",
            Color = "Black",
            ReorderThreshold = 10,
            UnitOfMeasure = UnitOfMeasure.Pairs
        };

        var createdProduct = new ProductDto
        {
            Id = 1,
            Name = createDto.Name,
            Category = createDto.Category,
            Specifications = createDto.Specifications,
            Grade = createDto.Grade,
            Color = createDto.Color,
            ReorderThreshold = createDto.ReorderThreshold,
            UnitOfMeasure = createDto.UnitOfMeasure
        };

        _mockProductService.Setup(s => s.CreateProductAsync(createDto))
            .ReturnsAsync(createdProduct);

        // Act
        var result = await _controller.CreateProduct(createDto);

        // Assert
        result.Result.Should().BeOfType<CreatedAtActionResult>();
        var createdResult = result.Result as CreatedAtActionResult;
        createdResult!.Value.Should().BeEquivalentTo(createdProduct);
        createdResult.ActionName.Should().Be(nameof(ProductsController.GetProduct));
        createdResult.RouteValues!["id"].Should().Be(createdProduct.Id);
    }

    [Fact]
    public async Task CreateProduct_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var createDto = new CreateProductDto(); // Invalid - missing required fields
        _controller.ModelState.AddModelError("Name", "Name is required");

        // Act
        var result = await _controller.CreateProduct(createDto);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task UpdateProduct_WithValidData_ShouldReturnOkWithUpdatedProduct()
    {
        // Arrange
        var productId = 1;
        var updateDto = new UpdateProductDto
        {
            Name = "Updated Product",
            Category = ProductCategory.Leather
        };

        var updatedProduct = new ProductDto
        {
            Id = productId,
            Name = updateDto.Name,
            Category = updateDto.Category
        };

        _mockProductService.Setup(s => s.UpdateProductAsync(productId, updateDto))
            .ReturnsAsync(updatedProduct);

        // Act
        var result = await _controller.UpdateProduct(productId, updateDto);

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(updatedProduct);
    }

    [Fact]
    public async Task UpdateProduct_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var productId = 999;
        var updateDto = new UpdateProductDto { Name = "Updated Product" };

        _mockProductService.Setup(s => s.UpdateProductAsync(productId, updateDto))
            .ReturnsAsync((ProductDto?)null);

        // Act
        var result = await _controller.UpdateProduct(productId, updateDto);

        // Assert
        result.Result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task DeleteProduct_WithValidId_ShouldReturnNoContent()
    {
        // Arrange
        var productId = 1;
        _mockProductService.Setup(s => s.DeleteProductAsync(productId))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.DeleteProduct(productId);

        // Assert
        result.Should().BeOfType<NoContentResult>();
    }

    [Fact]
    public async Task DeleteProduct_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var productId = 999;
        _mockProductService.Setup(s => s.DeleteProductAsync(productId))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.DeleteProduct(productId);

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task GetLowStockProducts_ShouldReturnOkWithLowStockProducts()
    {
        // Arrange
        var lowStockProducts = new List<ProductDto>
        {
            new() { Id = 1, Name = "Low Stock Product", CurrentQuantity = 5, ReorderThreshold = 10 }
        };

        _mockProductService.Setup(s => s.GetLowStockProductsAsync())
            .ReturnsAsync(lowStockProducts);

        // Act
        var result = await _controller.GetLowStockProducts();

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(lowStockProducts);
    }

    [Fact]
    public async Task SearchProducts_WithSearchTerm_ShouldReturnMatchingProducts()
    {
        // Arrange
        var searchTerm = "Nike";
        var searchResults = new List<ProductDto>
        {
            new() { Id = 1, Name = "Nike Air Max" },
            new() { Id = 2, Name = "Nike Revolution" }
        };

        _mockProductService.Setup(s => s.SearchProductsAsync(searchTerm))
            .ReturnsAsync(searchResults);

        // Act
        var result = await _controller.SearchProducts(searchTerm);

        // Assert
        result.Result.Should().BeOfType<OkObjectResult>();
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(searchResults);
    }

    [Fact]
    public async Task SearchProducts_WithEmptySearchTerm_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.SearchProducts("");

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
    }

    // Helper DTOs for testing
    public class CreateProductDto
    {
        public string Name { get; set; } = string.Empty;
        public ProductCategory Category { get; set; }
        public string Specifications { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal ReorderThreshold { get; set; }
        public UnitOfMeasure UnitOfMeasure { get; set; }
    }

    public class UpdateProductDto
    {
        public string Name { get; set; } = string.Empty;
        public ProductCategory Category { get; set; }
        public string Specifications { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal ReorderThreshold { get; set; }
        public UnitOfMeasure UnitOfMeasure { get; set; }
    }
}
