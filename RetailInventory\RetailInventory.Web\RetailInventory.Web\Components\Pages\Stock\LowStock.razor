@page "/stock/low-stock"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Low Stock Alert - Retail Inventory</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <div class="d-flex justify-space-between align-center mb-4">
        <div>
            <MudText Typo="Typo.h4">⚠️ Low Stock Alert</MudText>
            <MudText Typo="Typo.body1" Color="Color.Default">
                Products that need immediate attention
            </MudText>
        </div>
        <MudButton Variant="Variant.Filled" 
                   Color="Color.Primary" 
                   StartIcon="@Icons.Material.Filled.Refresh"
                   OnClick="LoadLowStockItems">
            Refresh
        </MudButton>
    </div>

    @if (_loading)
    {
        <div class="d-flex justify-center pa-8">
            <MudProgressCircular Indeterminate="true" Size="Size.Large" />
        </div>
    }
    else if (_lowStockItems?.Any() == true)
    {
        <!-- Summary Cards -->
        <MudGrid Class="mb-4">
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_lowStockItems.Count</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Items Low on Stock</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.Error" Color="Color.Error" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_outOfStockCount</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Out of Stock</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Color="Color.Success" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">₹@_estimatedReorderCost.ToString("N0")</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Est. Reorder Cost</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent Class="pa-0">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.Category" Color="Color.Info" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_affectedCategories</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">Categories Affected</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- Low Stock Items Table -->
        <MudCard Elevation="2">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">📋 Items Requiring Attention</MudText>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Success" 
                               StartIcon="@Icons.Material.Filled.ShoppingCart"
                               OnClick="BulkReorder"
                               Disabled="_selectedItems.Count == 0">
                        Bulk Reorder (@_selectedItems.Count)
                    </MudButton>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent Class="pa-0">
                <MudTable Items="_lowStockItems" 
                          MultiSelection="true" 
                          @bind-SelectedItems="_selectedItems"
                          Hover="true" 
                          Striped="true" 
                          Dense="true"
                          FixedHeader="true"
                          Height="500px">
                    <HeaderContent>
                        <MudTh>Product</MudTh>
                        <MudTh>Category</MudTh>
                        <MudTh>Current Stock</MudTh>
                        <MudTh>Reorder Level</MudTh>
                        <MudTh>Shortage</MudTh>
                        <MudTh>Last Cost</MudTh>
                        <MudTh>Priority</MudTh>
                        <MudTh>Actions</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>
                            <div>
                                <MudText Typo="Typo.body1"><strong>@context.Name</strong></MudText>
                                <MudText Typo="Typo.body2" Color="Color.Default">
                                    @context.Specifications | @context.Color
                                </MudText>
                            </div>
                        </MudTd>
                        <MudTd>
                            <MudChip Size="Size.Small" Color="GetCategoryColor(context.Category)">
                                @context.Category
                            </MudChip>
                        </MudTd>
                        <MudTd>
                            <div class="d-flex align-center">
                                <MudText Typo="Typo.body1" Color="@(context.CurrentQuantity == 0 ? Color.Error : Color.Warning)">
                                    <strong>@context.CurrentQuantity.ToString("N0")</strong>
                                </MudText>
                                <MudText Typo="Typo.body2" Class="ml-1">@GetUnitDisplay(context.UnitOfMeasure)</MudText>
                            </div>
                        </MudTd>
                        <MudTd>@context.ReorderThreshold.ToString("N0")</MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body1" Color="Color.Error">
                                <strong>@((context.ReorderThreshold - context.CurrentQuantity).ToString("N0"))</strong>
                            </MudText>
                        </MudTd>
                        <MudTd>₹@context.AverageCostPerUnit.ToString("N0")</MudTd>
                        <MudTd>
                            @if (context.CurrentQuantity == 0)
                            {
                                <MudChip Size="Size.Small" Color="Color.Error" Icon="@Icons.Material.Filled.PriorityHigh">
                                    Critical
                                </MudChip>
                            }
                            else if (context.CurrentQuantity <= context.ReorderThreshold * 0.5m)
                            {
                                <MudChip Size="Size.Small" Color="Color.Warning" Icon="@Icons.Material.Filled.Warning">
                                    High
                                </MudChip>
                            }
                            else
                            {
                                <MudChip Size="Size.Small" Color="Color.Info" Icon="@Icons.Material.Filled.Info">
                                    Medium
                                </MudChip>
                            }
                        </MudTd>
                        <MudTd>
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Add" 
                                               Color="Color.Success" 
                                               Size="Size.Small"
                                               OnClick="() => QuickReorder(context)"
                                               Title="Quick Reorder" />
                                <MudIconButton Icon="@Icons.Material.Filled.Inventory" 
                                               Color="Color.Primary" 
                                               Size="Size.Small"
                                               OnClick="() => AddStock(context.Id)"
                                               Title="Add Stock" />
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                               Color="Color.Secondary" 
                                               Size="Size.Small"
                                               OnClick="() => EditProduct(context.Id)"
                                               Title="Edit Product" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudCardContent>
        </MudCard>

        <!-- Quick Actions -->
        <MudCard Elevation="2" Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">⚡ Quick Actions</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudGrid>
                    <MudItem xs="12" md="3">
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Primary" 
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.AutoAwesome"
                                   Href="/stock/reorder">
                            🔮 AI Reorder Suggestions
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" md="3">
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Secondary" 
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.FileDownload"
                                   OnClick="ExportLowStockReport">
                            Export Report
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" md="3">
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Info" 
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.Settings"
                                   OnClick="UpdateReorderThresholds">
                            Update Thresholds
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" md="3">
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Success" 
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   Href="/products/add">
                            Add New Product
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }
    else
    {
        <!-- No Low Stock Items -->
        <MudCard Elevation="2" Class="pa-8">
            <div class="d-flex flex-column align-center">
                <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" Color="Color.Success" Class="mb-4" />
                <MudText Typo="Typo.h5" Color="Color.Success" Class="mb-2">🎉 All Good!</MudText>
                <MudText Typo="Typo.body1" Color="Color.Default" Class="mb-4">
                    No products are currently low on stock. Your inventory levels look healthy!
                </MudText>
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           StartIcon="@Icons.Material.Filled.Inventory"
                           Href="/products">
                    View All Products
                </MudButton>
            </div>
        </MudCard>
    }
</MudContainer>

@code {
    private List<ProductDto>? _lowStockItems;
    private HashSet<ProductDto> _selectedItems = new();
    private bool _loading = true;

    private int _outOfStockCount => _lowStockItems?.Count(p => p.CurrentQuantity == 0) ?? 0;
    private decimal _estimatedReorderCost => _lowStockItems?.Sum(p => (p.ReorderThreshold - p.CurrentQuantity) * p.AverageCostPerUnit) ?? 0;
    private int _affectedCategories => _lowStockItems?.Select(p => p.Category).Distinct().Count() ?? 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadLowStockItems();
    }

    private async Task LoadLowStockItems()
    {
        try
        {
            _loading = true;
            var response = await Http.GetFromJsonAsync<List<ProductDto>>("api/stock/low-stock");
            _lowStockItems = response ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading low stock items: {ex.Message}", Severity.Error);
            _lowStockItems = new List<ProductDto>();
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task QuickReorder(ProductDto product)
    {
        var suggestedQuantity = product.ReorderThreshold * 2; // Order double the threshold
        var estimatedCost = suggestedQuantity * product.AverageCostPerUnit;
        
        Snackbar.Add(
            $"Quick reorder {suggestedQuantity:N0} {GetUnitDisplay(product.UnitOfMeasure)} of {product.Name}? Est. cost: ₹{estimatedCost:N2}",
            Severity.Info);

        // Navigate to receive stock page with pre-filled data
        Navigation.NavigateTo($"/stock/receive?productId={product.Id}&quantity={suggestedQuantity}");
    }

    private void BulkReorder()
    {
        if (_selectedItems.Count == 0)
        {
            Snackbar.Add("Please select items to reorder", Severity.Warning);
            return;
        }

        var totalCost = _selectedItems.Sum(p => (p.ReorderThreshold * 2) * p.AverageCostPerUnit);
        Snackbar.Add($"Bulk reorder feature coming soon! Est. cost for {_selectedItems.Count} items: ₹{totalCost:N2}", Severity.Info);
    }

    private void ExportLowStockReport()
    {
        Snackbar.Add("Export functionality coming soon!", Severity.Info);
    }

    private void UpdateReorderThresholds()
    {
        Snackbar.Add("Bulk threshold update coming soon!", Severity.Info);
    }

    private void AddStock(int productId)
    {
        Navigation.NavigateTo($"/stock/receive?productId={productId}");
    }

    private void EditProduct(int productId)
    {
        Navigation.NavigateTo($"/products/{productId}/edit");
    }

    private Color GetCategoryColor(ProductCategory category)
    {
        return category switch
        {
            ProductCategory.Footwear => Color.Primary,
            ProductCategory.Leather => Color.Secondary,
            ProductCategory.Clothing => Color.Tertiary,
            ProductCategory.Accessories => Color.Info,
            _ => Color.Default
        };
    }

    private string GetUnitDisplay(UnitOfMeasure unit)
    {
        return unit switch
        {
            UnitOfMeasure.Pairs => "pairs",
            UnitOfMeasure.Pieces => "pcs",
            UnitOfMeasure.Kilograms => "kg",
            UnitOfMeasure.Meters => "m",
            UnitOfMeasure.Liters => "L",
            _ => "units"
        };
    }
}
