using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Services;

public class StubReportingService : IReportingService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly Random _random;

    public StubReportingService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
        _random = new Random();
    }

    public async Task<DashboardMetricsDto> GetDashboardMetricsAsync()
    {
        await Task.Delay(100); // Simulate async operation

        return new DashboardMetricsDto
        {
            TodaysSales = 45230m,
            TodaysProfit = 12450m,
            LowStockCount = 3,
            InventoryValue = 2850000m,
            WeeklySales = 285600m,
            WeeklyProfit = 78400m,
            MonthlySales = 1245000m,
            MonthlyProfit = 342000m,
            TotalProducts = 6,
            OutOfStockCount = 0,
            AverageProfitMargin = 27.5m,
            TotalSalesToday = 23
        };
    }

    public async Task<List<SalesTrendDto>> GetSalesTrendAsync(DateTime startDate, DateTime endDate, string groupBy = "Day")
    {
        await Task.Delay(200);

        var trends = new List<SalesTrendDto>();
        var current = startDate;

        while (current <= endDate)
        {
            var baseRevenue = 35000m + (_random.Next(-5000, 10000));
            trends.Add(new SalesTrendDto
            {
                Date = current,
                Revenue = baseRevenue,
                Profit = baseRevenue * 0.275m,
                SalesCount = _random.Next(15, 35),
                AverageOrderValue = baseRevenue / _random.Next(15, 35)
            });

            current = groupBy switch
            {
                "Week" => current.AddDays(7),
                "Month" => current.AddMonths(1),
                _ => current.AddDays(1)
            };
        }

        return trends;
    }

    public async Task<List<ProductPerformanceDto>> GetTopPerformingProductsAsync(int count = 10, DateTime? startDate = null, DateTime? endDate = null)
    {
        await Task.Delay(150);

        var products = new List<ProductPerformanceDto>
        {
            new() { ProductId = 1, ProductName = "Nike Air Max", Category = "Footwear", TotalRevenue = 135000m, TotalProfit = 37125m, UnitsSold = 30, ProfitMargin = 27.5m, SalesCount = 15 },
            new() { ProductId = 2, ProductName = "Adidas Ultraboost", Category = "Footwear", TotalRevenue = 124000m, TotalProfit = 34100m, UnitsSold = 20, ProfitMargin = 27.5m, SalesCount = 12 },
            new() { ProductId = 3, ProductName = "Formal Oxford Shoes", Category = "Footwear", TotalRevenue = 96000m, TotalProfit = 26400m, UnitsSold = 30, ProfitMargin = 27.5m, SalesCount = 18 },
            new() { ProductId = 4, ProductName = "Women's Heels", Category = "Footwear", TotalRevenue = 84000m, TotalProfit = 23100m, UnitsSold = 30, ProfitMargin = 27.5m, SalesCount = 20 },
            new() { ProductId = 5, ProductName = "Casual Sneakers", Category = "Footwear", TotalRevenue = 54000m, TotalProfit = 14850m, UnitsSold = 30, ProfitMargin = 27.5m, SalesCount = 25 },
            new() { ProductId = 6, ProductName = "Sports Running Shoes", Category = "Footwear", TotalRevenue = 105000m, TotalProfit = 28875m, UnitsSold = 30, ProfitMargin = 27.5m, SalesCount = 22 }
        };

        return products.Take(count).ToList();
    }

    public async Task<List<CategoryPerformanceDto>> GetCategoryPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        await Task.Delay(100);

        return new List<CategoryPerformanceDto>
        {
            new() { Category = "Footwear", TotalRevenue = 598000m, TotalProfit = 164450m, ProductCount = 6, UnitsSold = 170, ProfitMargin = 27.5m },
            new() { Category = "Accessories", TotalRevenue = 45000m, TotalProfit = 12375m, ProductCount = 2, UnitsSold = 25, ProfitMargin = 27.5m },
            new() { Category = "Clothing", TotalRevenue = 32000m, TotalProfit = 8800m, ProductCount = 1, UnitsSold = 15, ProfitMargin = 27.5m }
        };
    }

    public async Task<List<InventoryAnalyticsDto>> GetInventoryAnalyticsAsync()
    {
        await Task.Delay(150);

        return new List<InventoryAnalyticsDto>
        {
            new() { ProductId = 1, ProductName = "Nike Air Max", Category = "Footwear", CurrentStock = 33m, ReorderThreshold = 10m, StockValue = 105600m, DaysOfStock = 45, TurnoverRate = 8.1m, StockStatus = "Normal" },
            new() { ProductId = 2, ProductName = "Adidas Ultraboost", Category = "Footwear", CurrentStock = 75m, ReorderThreshold = 8m, StockValue = 337500m, DaysOfStock = 120, TurnoverRate = 3.0m, StockStatus = "Normal" },
            new() { ProductId = 3, ProductName = "Formal Oxford Shoes", Category = "Footwear", CurrentStock = 35m, ReorderThreshold = 15m, StockValue = 77000m, DaysOfStock = 35, TurnoverRate = 10.4m, StockStatus = "Normal" },
            new() { ProductId = 4, ProductName = "Women's Heels", Category = "Footwear", CurrentStock = 8m, ReorderThreshold = 12m, StockValue = 14400m, DaysOfStock = 12, TurnoverRate = 30.4m, StockStatus = "Low" },
            new() { ProductId = 5, ProductName = "Casual Sneakers", Category = "Footwear", CurrentStock = 5m, ReorderThreshold = 20m, StockValue = 6000m, DaysOfStock = 8, TurnoverRate = 45.6m, StockStatus = "Critical" },
            new() { ProductId = 6, ProductName = "Sports Running Shoes", Category = "Footwear", CurrentStock = 22m, ReorderThreshold = 15m, StockValue = 55000m, DaysOfStock = 25, TurnoverRate = 14.6m, StockStatus = "Normal" }
        };
    }

    public async Task<List<SalesSourceAnalyticsDto>> GetSalesSourceAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        await Task.Delay(100);

        return new List<SalesSourceAnalyticsDto>
        {
            new() { Source = "Manual", SalesCount = 45, TotalRevenue = 285000m, AverageOrderValue = 6333m, Percentage = 65.2m },
            new() { Source = "Voice", SalesCount = 18, TotalRevenue = 124000m, AverageOrderValue = 6889m, Percentage = 26.1m },
            new() { Source = "Receipt", SalesCount = 6, TotalRevenue = 38000m, AverageOrderValue = 6333m, Percentage = 8.7m }
        };
    }

    public async Task<List<ProfitabilityAnalysisDto>> GetProfitabilityAnalysisAsync(DateTime startDate, DateTime endDate, string groupBy = "Month")
    {
        await Task.Delay(200);

        var analysis = new List<ProfitabilityAnalysisDto>();
        var current = startDate;

        while (current <= endDate)
        {
            var revenue = 125000m + (_random.Next(-15000, 25000));
            var cost = revenue * 0.725m;
            var profit = revenue - cost;

            analysis.Add(new ProfitabilityAnalysisDto
            {
                Period = current,
                Revenue = revenue,
                Cost = cost,
                Profit = profit,
                ProfitMargin = (profit / revenue) * 100,
                GrossMargin = (profit / revenue) * 100
            });

            current = groupBy switch
            {
                "Week" => current.AddDays(7),
                "Month" => current.AddMonths(1),
                "Quarter" => current.AddMonths(3),
                _ => current.AddDays(1)
            };
        }

        return analysis;
    }

    public async Task<List<LowStockAlertDto>> GetLowStockAlertsAsync()
    {
        await Task.Delay(100);

        return new List<LowStockAlertDto>
        {
            new() { ProductId = 5, ProductName = "Casual Sneakers", Category = "Footwear", CurrentStock = 5m, ReorderThreshold = 20m, Shortage = 15m, Priority = "Critical", EstimatedReorderCost = 18000m, DaysUntilStockOut = 8 },
            new() { ProductId = 4, ProductName = "Women's Heels", Category = "Footwear", CurrentStock = 8m, ReorderThreshold = 12m, Shortage = 4m, Priority = "High", EstimatedReorderCost = 7200m, DaysUntilStockOut = 12 }
        };
    }

    public async Task<List<RevenueBreakdownDto>> GetRevenueBreakdownAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        await Task.Delay(100);

        return new List<RevenueBreakdownDto>
        {
            new() { Label = "Footwear", Amount = 598000m, Percentage = 89.2m, Color = "#1976d2" },
            new() { Label = "Accessories", Amount = 45000m, Percentage = 6.7m, Color = "#388e3c" },
            new() { Label = "Clothing", Amount = 28000m, Percentage = 4.1m, Color = "#f57c00" }
        };
    }

    public async Task<List<SalesVelocityDto>> GetSalesVelocityAsync()
    {
        await Task.Delay(150);

        return new List<SalesVelocityDto>
        {
            new() { ProductId = 1, ProductName = "Nike Air Max", DailySalesAverage = 1.2m, WeeklySalesAverage = 8.4m, MonthlySalesAverage = 36m, Trend = "Increasing", TrendPercentage = 15.2m },
            new() { ProductId = 2, ProductName = "Adidas Ultraboost", DailySalesAverage = 0.8m, WeeklySalesAverage = 5.6m, MonthlySalesAverage = 24m, Trend = "Stable", TrendPercentage = 2.1m },
            new() { ProductId = 3, ProductName = "Formal Oxford Shoes", DailySalesAverage = 1.5m, WeeklySalesAverage = 10.5m, MonthlySalesAverage = 45m, Trend = "Increasing", TrendPercentage = 8.7m }
        };
    }

    public async Task<FinancialSummaryDto> GetFinancialSummaryAsync(DateTime startDate, DateTime endDate)
    {
        await Task.Delay(150);

        var totalRevenue = 1245000m;
        var totalCost = 902625m;
        var grossProfit = totalRevenue - totalCost;

        return new FinancialSummaryDto
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalRevenue = totalRevenue,
            TotalCost = totalCost,
            GrossProfit = grossProfit,
            NetProfit = grossProfit * 0.85m, // After expenses
            ProfitMargin = (grossProfit / totalRevenue) * 100,
            TotalTransactions = 156,
            AverageTransactionValue = totalRevenue / 156,
            TaxAmount = totalRevenue * 0.18m // 18% GST
        };
    }

    public async Task<ChartDataDto> GetSalesChartDataAsync(DateTime startDate, DateTime endDate, string groupBy = "Day")
    {
        await Task.Delay(200);

        var labels = new List<string>();
        var salesData = new List<decimal>();
        var current = startDate;

        while (current <= endDate && labels.Count < 30) // Limit to 30 data points
        {
            labels.Add(current.ToString(groupBy == "Month" ? "MMM yyyy" : "MMM dd"));
            salesData.Add(35000m + (_random.Next(-5000, 10000)));

            current = groupBy switch
            {
                "Week" => current.AddDays(7),
                "Month" => current.AddMonths(1),
                _ => current.AddDays(1)
            };
        }

        return new ChartDataDto
        {
            Labels = labels,
            Datasets = new List<ChartDatasetDto>
            {
                new()
                {
                    Label = "Sales Revenue",
                    Data = salesData,
                    BackgroundColor = "rgba(25, 118, 210, 0.1)",
                    BorderColor = "rgba(25, 118, 210, 1)",
                    BorderWidth = 2,
                    Fill = true
                }
            }
        };
    }

    public async Task<ChartDataDto> GetProfitChartDataAsync(DateTime startDate, DateTime endDate, string groupBy = "Day")
    {
        await Task.Delay(200);

        var salesChart = await GetSalesChartDataAsync(startDate, endDate, groupBy);
        var profitData = salesChart.Datasets[0].Data.Select(x => x * 0.275m).ToList();

        return new ChartDataDto
        {
            Labels = salesChart.Labels,
            Datasets = new List<ChartDatasetDto>
            {
                new()
                {
                    Label = "Profit",
                    Data = profitData,
                    BackgroundColor = "rgba(56, 142, 60, 0.1)",
                    BorderColor = "rgba(56, 142, 60, 1)",
                    BorderWidth = 2,
                    Fill = true
                }
            }
        };
    }

    public async Task<ChartDataDto> GetCategoryRevenueChartDataAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        await Task.Delay(100);

        return new ChartDataDto
        {
            Labels = new List<string> { "Footwear", "Accessories", "Clothing" },
            Datasets = new List<ChartDatasetDto>
            {
                new()
                {
                    Label = "Revenue by Category",
                    Data = new List<decimal> { 598000m, 45000m, 28000m },
                    BackgroundColor = "rgba(25, 118, 210, 0.8)",
                    BorderColor = "rgba(25, 118, 210, 1)",
                    BorderWidth = 1
                }
            }
        };
    }

    public async Task<ChartDataDto> GetInventoryValueChartDataAsync()
    {
        await Task.Delay(100);

        return new ChartDataDto
        {
            Labels = new List<string> { "Nike Air Max", "Adidas Ultraboost", "Oxford Shoes", "Women's Heels", "Sneakers", "Running Shoes" },
            Datasets = new List<ChartDatasetDto>
            {
                new()
                {
                    Label = "Inventory Value",
                    Data = new List<decimal> { 105600m, 337500m, 77000m, 14400m, 6000m, 55000m },
                    BackgroundColor = "rgba(245, 124, 0, 0.8)",
                    BorderColor = "rgba(245, 124, 0, 1)",
                    BorderWidth = 1
                }
            }
        };
    }
}
