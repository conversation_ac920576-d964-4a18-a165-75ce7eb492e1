namespace RetailInventory.Application.DTOs;

public class ReorderSuggestionDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal CurrentStock { get; set; }
    public decimal ReorderThreshold { get; set; }
    public decimal SuggestedOrderQuantity { get; set; }
    public decimal AverageDailySales { get; set; }
    public int DaysUntilStockOut { get; set; }
    public string UnitOfMeasure { get; set; } = string.Empty;
    public ReorderPriority Priority { get; set; }
    public string TrendIndicator { get; set; } = string.Empty; // ↗️ ↘️ ➡️
    public decimal EstimatedCost { get; set; }
    public string Reason { get; set; } = string.Empty;
    public DateTime LastRestocked { get; set; }
    public int LeadTimeDays { get; set; }
}

public class ReorderRadarResultDto
{
    public List<ReorderSuggestionDto> Suggestions { get; set; } = new();
    public int TotalCriticalItems { get; set; }
    public int TotalHighPriorityItems { get; set; }
    public decimal TotalEstimatedCost { get; set; }
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public string Summary { get; set; } = string.Empty;
}

public class ReorderSalesVelocityDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal Last7DaysAverage { get; set; }
    public decimal Last30DaysAverage { get; set; }
    public decimal TrendPercentage { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
    public int TotalSalesLast30Days { get; set; }
}

public enum ReorderPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}
