namespace RetailInventory.Application.DTOs;

public class DashboardMetricsDto
{
    public decimal TodaysSales { get; set; }
    public decimal TodaysProfit { get; set; }
    public int LowStockCount { get; set; }
    public decimal InventoryValue { get; set; }
    public decimal WeeklySales { get; set; }
    public decimal WeeklyProfit { get; set; }
    public decimal MonthlySales { get; set; }
    public decimal MonthlyProfit { get; set; }
    public int TotalProducts { get; set; }
    public int OutOfStockCount { get; set; }
    public decimal AverageProfitMargin { get; set; }
    public int TotalSalesToday { get; set; }
}

public class SalesTrendDto
{
    public DateTime Date { get; set; }
    public decimal Revenue { get; set; }
    public decimal Profit { get; set; }
    public int SalesCount { get; set; }
    public decimal AverageOrderValue { get; set; }
}

public class ProductPerformanceDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal TotalProfit { get; set; }
    public int UnitsSold { get; set; }
    public decimal ProfitMargin { get; set; }
    public int SalesCount { get; set; }
}

public class CategoryPerformanceDto
{
    public string Category { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal TotalProfit { get; set; }
    public int ProductCount { get; set; }
    public int UnitsSold { get; set; }
    public decimal ProfitMargin { get; set; }
}

public class InventoryAnalyticsDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal CurrentStock { get; set; }
    public decimal ReorderThreshold { get; set; }
    public decimal StockValue { get; set; }
    public int DaysOfStock { get; set; }
    public decimal TurnoverRate { get; set; }
    public string StockStatus { get; set; } = string.Empty; // "Normal", "Low", "Critical", "Out"
}

public class SalesSourceAnalyticsDto
{
    public string Source { get; set; } = string.Empty; // "Manual", "Voice", "Receipt"
    public int SalesCount { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal Percentage { get; set; }
}

public class ProfitabilityAnalysisDto
{
    public DateTime Period { get; set; }
    public decimal Revenue { get; set; }
    public decimal Cost { get; set; }
    public decimal Profit { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal GrossMargin { get; set; }
}

public class TopSellingProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int UnitsSold { get; set; }
    public decimal Revenue { get; set; }
    public decimal Profit { get; set; }
    public int SalesCount { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
}

public class LowStockAlertDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal CurrentStock { get; set; }
    public decimal ReorderThreshold { get; set; }
    public decimal Shortage { get; set; }
    public string Priority { get; set; } = string.Empty; // "Critical", "High", "Medium"
    public decimal EstimatedReorderCost { get; set; }
    public int DaysUntilStockOut { get; set; }
}

public class RevenueBreakdownDto
{
    public string Label { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public string Color { get; set; } = string.Empty;
}

public class SalesVelocityDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal DailySalesAverage { get; set; }
    public decimal WeeklySalesAverage { get; set; }
    public decimal MonthlySalesAverage { get; set; }
    public string Trend { get; set; } = string.Empty; // "Increasing", "Stable", "Decreasing"
    public decimal TrendPercentage { get; set; }
}

public class FinancialSummaryDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal TotalCost { get; set; }
    public decimal GrossProfit { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public int TotalTransactions { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public decimal TaxAmount { get; set; }
}

public class ChartDataDto
{
    public List<string> Labels { get; set; } = new();
    public List<ChartDatasetDto> Datasets { get; set; } = new();
}

public class ChartDatasetDto
{
    public string Label { get; set; } = string.Empty;
    public List<decimal> Data { get; set; } = new();
    public string BackgroundColor { get; set; } = string.Empty;
    public string BorderColor { get; set; } = string.Empty;
    public int BorderWidth { get; set; } = 1;
    public bool Fill { get; set; } = false;
}

public class ReportFilterDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public List<string> Categories { get; set; } = new();
    public List<string> Sources { get; set; } = new();
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public string GroupBy { get; set; } = "Day"; // "Day", "Week", "Month", "Quarter", "Year"
}
