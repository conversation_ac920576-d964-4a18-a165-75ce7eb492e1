using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Enums;
using RetailInventory.Infrastructure.DbContext;
using RetailInventory.Infrastructure.Repositories;

namespace RetailInventory.Infrastructure.Tests.Repositories;

public class ProductRepositoryTests : IDisposable
{
    private readonly RetailInventoryDbContext _context;
    private readonly Repository<Product> _repository;

    public ProductRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<RetailInventoryDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new RetailInventoryDbContext(options);
        _repository = new Repository<Product>(_context);
    }

    [Fact]
    public async Task AddAsync_ShouldAddProductToDatabase()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Category = ProductCategory.Footwear,
            Specifications = "Size 8-12",
            Grade = "Premium",
            Color = "Black",
            ReorderThreshold = 10,
            UnitOfMeasure = UnitOfMeasure.Pairs,
            IsActive = true
        };

        // Act
        await _repository.AddAsync(product);
        await _context.SaveChangesAsync();

        // Assert
        product.Id.Should().BeGreaterThan(0);

        var savedProduct = await _context.Products.FindAsync(product.Id);
        savedProduct.Should().NotBeNull();
        savedProduct!.Name.Should().Be("Test Product");
    }

    [Fact]
    public async Task GetByIdAsync_WithValidId_ShouldReturnProduct()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Category = ProductCategory.Footwear,
            IsActive = true
        };

        _context.Products.Add(product);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(product.Id);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(product.Id);
        result.Name.Should().Be("Test Product");
    }

    [Fact]
    public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllProducts()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Name = "Product 1", Category = ProductCategory.Footwear, IsActive = true },
            new() { Name = "Product 2", Category = ProductCategory.Leather, IsActive = true },
            new() { Name = "Product 3", Category = ProductCategory.Clothing, IsActive = false }
        };

        _context.Products.AddRange(products);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllAsync();

        // Assert
        result.Should().HaveCount(3);
        result.Should().Contain(p => p.Name == "Product 1");
        result.Should().Contain(p => p.Name == "Product 2");
        result.Should().Contain(p => p.Name == "Product 3");
    }

    [Fact]
    public async Task FindAsync_WithPredicate_ShouldReturnMatchingProducts()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Name = "Nike Air Max", Category = ProductCategory.Footwear, IsActive = true },
            new() { Name = "Adidas Ultraboost", Category = ProductCategory.Footwear, IsActive = true },
            new() { Name = "Leather Jacket", Category = ProductCategory.Leather, IsActive = true }
        };

        _context.Products.AddRange(products);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindAsync(p => p.Category == ProductCategory.Footwear);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(p => p.Category == ProductCategory.Footwear);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateProductInDatabase()
    {
        // Arrange
        var product = new Product
        {
            Name = "Original Name",
            Category = ProductCategory.Footwear,
            IsActive = true
        };

        _context.Products.Add(product);
        await _context.SaveChangesAsync();

        // Modify the product
        product.Name = "Updated Name";
        product.Category = ProductCategory.Leather;

        // Act
        _repository.Update(product);
        await _context.SaveChangesAsync();

        // Assert
        var updatedProduct = await _context.Products.FindAsync(product.Id);
        updatedProduct.Should().NotBeNull();
        updatedProduct!.Name.Should().Be("Updated Name");
        updatedProduct.Category.Should().Be(ProductCategory.Leather);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveProductFromDatabase()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Category = ProductCategory.Footwear,
            IsActive = true
        };

        _context.Products.Add(product);
        await _context.SaveChangesAsync();

        // Act
        _repository.Remove(product);
        await _context.SaveChangesAsync();

        // Assert
        var deletedProduct = await _context.Products.FindAsync(product.Id);
        deletedProduct.Should().BeNull();
    }

    [Fact]
    public async Task FindAsync_WithLowStockPredicate_ShouldReturnProductsBelowThreshold()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Name = "Low Stock 1", CurrentQuantity = 5, ReorderThreshold = 10, IsActive = true },
            new() { Name = "Low Stock 2", CurrentQuantity = 8, ReorderThreshold = 10, IsActive = true },
            new() { Name = "Normal Stock", CurrentQuantity = 15, ReorderThreshold = 10, IsActive = true },
            new() { Name = "Inactive Low Stock", CurrentQuantity = 3, ReorderThreshold = 10, IsActive = false }
        };

        _context.Products.AddRange(products);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindAsync(p => p.CurrentQuantity <= p.ReorderThreshold && p.IsActive);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(p => p.CurrentQuantity <= p.ReorderThreshold && p.IsActive);
        result.Should().Contain(p => p.Name == "Low Stock 1");
        result.Should().Contain(p => p.Name == "Low Stock 2");
    }

    [Fact]
    public async Task FindAsync_WithNameSearchPredicate_ShouldReturnMatchingProducts()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Name = "Nike Air Max", Specifications = "Running shoes", IsActive = true },
            new() { Name = "Adidas Ultraboost", Specifications = "Comfort shoes", IsActive = true },
            new() { Name = "Puma Sneakers", Specifications = "Casual wear", IsActive = true }
        };

        _context.Products.AddRange(products);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindAsync(p => p.Name.Contains("Nike"));

        // Assert
        result.Should().HaveCount(1);
        result.First().Name.Should().Be("Nike Air Max");
    }

    [Fact]
    public async Task FindAsync_WithCategoryPredicate_ShouldReturnProductsInCategory()
    {
        // Arrange
        var products = new List<Product>
        {
            new() { Name = "Sneakers", Category = ProductCategory.Footwear, IsActive = true },
            new() { Name = "Boots", Category = ProductCategory.Footwear, IsActive = true },
            new() { Name = "Jacket", Category = ProductCategory.Leather, IsActive = true }
        };

        _context.Products.AddRange(products);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindAsync(p => p.Category == ProductCategory.Footwear);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(p => p.Category == ProductCategory.Footwear);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
