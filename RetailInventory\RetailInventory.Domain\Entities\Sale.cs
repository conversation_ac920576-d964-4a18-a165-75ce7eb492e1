namespace RetailInventory.Domain.Entities;

public class Sale
{
    public int Id { get; set; }
    public DateTime SaleDate { get; set; } = DateTime.UtcNow;
    public decimal TotalAmount { get; set; }
    public decimal TotalCost { get; set; }
    public decimal TotalProfit { get; set; }
    public int? CustomerId { get; set; }
    public string Notes { get; set; } = string.Empty;
    public string Source { get; set; } = "Manual"; // Manual, Voice, Receipt
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<SaleLine> SaleLines { get; set; } = new List<SaleLine>();

    // Business methods
    public void CalculateTotals()
    {
        TotalAmount = SaleLines.Sum(sl => sl.LineTotal);
        TotalCost = SaleLines.Sum(sl => sl.CostPerUnit * sl.Quantity);
        TotalProfit = TotalAmount - TotalCost;
    }

    public decimal GetProfitMargin()
    {
        return TotalAmount > 0 ? (TotalProfit / TotalAmount) * 100 : 0;
    }

    public decimal GetTotalItemsSold()
    {
        return SaleLines.Sum(sl => sl.Quantity);
    }

    public void AddSaleLine(SaleLine saleLine)
    {
        ArgumentNullException.ThrowIfNull(saleLine);
        SaleLines.Add(saleLine);
        saleLine.Sale = this;
        saleLine.SaleId = Id;
        CalculateTotals();
    }

    public bool RemoveSaleLine(SaleLine saleLine)
    {
        var removed = SaleLines.Remove(saleLine);
        if (removed)
        {
            CalculateTotals();
        }
        return removed;
    }

    public bool IsValidSale()
    {
        return SaleLines.Any() && SaleLines.All(sl => sl.Quantity > 0 && sl.SalePricePerUnit > 0);
    }

    public decimal ProfitMargin => TotalAmount > 0 ? (TotalProfit / TotalAmount) * 100 : 0;

    public int TotalItemsSold => SaleLines.Sum(sl => (int)sl.Quantity);
}
